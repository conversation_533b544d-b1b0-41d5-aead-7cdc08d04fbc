/* eslint-disable max-lines */
import styled from '@emotion/styled';
import {Flex, Typography} from 'antd';
import {ReactNode} from 'react';
import {
    IconArrowRight,
    IconDev,
    IconOps,
    IconTest,
    IconDevBg1,
    IconDevBg2,
    IconOpsBg1,
    IconOpsBg2,
    IconTestBg1,
    IconTestBg2,
} from '@/icons/mcp';
import {MCPZoneLink} from '@/links/mcp';
import {DEVELOP_ZONE_ID, OPERATION_ZONE_ID, TEST_ZONE_ID} from '../../MCPZone/constant';

const NavigationContainer = styled(Flex)`
    margin-top: 20px;
    gap: 12px;
`;

const RegionCard = styled.div<{ isActive?: boolean, disabled?: boolean, regionType?: string }>`
    flex: 1;
    padding: 20px 14px 26px;
    border-radius: 4px;
    background: ${props => {
        switch (props.regionType) {
            case 'dev':
                return 'linear-gradient(263.38deg, rgba(238, 249, 255, 0.9) -19.47%, #FFFFFF 99.07%)';
            case 'ops':
                return 'linear-gradient(263.38deg, rgba(238, 238, 255, 0.9) -19.47%, #FFFFFF 99.07%)';
            case 'test':
                return 'linear-gradient(263.73deg, rgba(243, 236, 255, 0.9) -76.36%, #FFFFFF 99.08%)';
        }
    }};
    border: 1px solid;
    border-image-source: ${props => {
        switch (props.regionType) {
            case 'dev':
                return 'linear-gradient(264.46deg, #96CDFF -128.22%, #F8F8F8 79.81%)';
            case 'ops':
                return 'linear-gradient(263.54deg, #BBBCFF -137.1%, #F8F8F8 99.42%)';
            case 'test':
                return 'linear-gradient(261.75deg, #E1AAFF -138.35%, #F8F8F8 96.74%)';
            default:
                return 'none';
        }
    }};
    transition: all 0.3s ease;
    position: relative;
    &:hover {
        ${props =>
        !props.disabled
            && `
            border: 1px solid #0083FF
            background: linear-gradient(328.39deg, #DEF3FF -8.86%, #FFFFFF 89.14%);
            box-shadow: 0px 5px 16px 0px #00000021;

        `}
    }
`;

const CardHeader = styled(Flex)`
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;
    position: relative;
    z-index: 2;
`;

const IconWrapper = styled.div`
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    svg {
        font-size: 26px;
    }
`;

const CardTitle = styled(Typography.Title)`
    font-size: 16px !important;
    font-weight: 600 !important;
    line-height: 28px !important;
`;

const CardDescription = styled(Typography.Text)`
    font-size: 14px;
    line-height: 22px;
    color: #545454;
    position: relative;
    z-index: 2;
`;

const HoverText = styled.span`
    color: #0083FF;
    font-size: 12px;
    line-height: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
    white-space: nowrap;
`;

const ArrowIcon = styled(IconArrowRight)`
    color: #8c8c8c;
    transition: all 0.3s ease;
    font-size: 16px;

    svg {
        transition: all 0.3s ease;
    }

    path {
        fill: #8c8c8c !important;
        transition: fill 0.3s ease;
    }
`;

const BackgroundElement = styled.div`
    position: absolute;
    z-index: 1;
    pointer-events: none;
`;

const StyledRegionCard = styled(RegionCard)`
    cursor: pointer;
    &:hover {
        ${HoverText} {
            opacity: 1;
        }

        ${ArrowIcon} {
            color: #0083FF;
            path {
                fill: #0083FF !important;
            }
        }
    }
`;

const enum ZONE_STATUS {
    ACTIVE = 'active',
    COMING = 'coming',
}

interface RegionItem {
    key: string;
    title: string;
    description: string;
    icon: ReactNode;
    status?: ZONE_STATUS;
    disabled?: boolean;
    onClick?: () => void;
    // 用id来跳转
    id: string;
}

interface Props {
    activeRegion?: string;
    onRegionChange?: (region: string) => void;
}

const RegionNavigation = ({activeRegion = 'dev', onRegionChange}: Props) => {
    const regions: RegionItem[] = [
        {
            key: 'dev',
            title: '开发专区',
            description:
                '面向研发场景提供丰富且高质量的MCP工具，覆盖DevOps 研发流程及AI应用开发的各环节，助力研发提效。 ',
            icon: <IconDev />,
            status: ZONE_STATUS.ACTIVE,
            disabled: false,
            id: DEVELOP_ZONE_ID,
        },
        {
            key: 'ops',
            title: '运维专区',
            description:
                '面向运维的MCP中心，汇聚各类高质量工具，在通用运维、云运维、业务运维等场景助力快速定位故障、分析根因、提升排障效率。',
            icon: <IconOps />,
            status: ZONE_STATUS.ACTIVE,
            disabled: false,
            id: OPERATION_ZONE_ID,
        },
        {
            key: 'test',
            title: '测试专区',
            description:
                '由各QA团队共建共享常用的测试工具、测试平台MCP Server，加速测试流程智能化改造。',
            icon: <IconTest />,
            status: ZONE_STATUS.COMING,
            disabled: false,
            id: TEST_ZONE_ID,
        },
    ];

    const handleRegionClick = (region: RegionItem) => {
        if (!region.disabled && region.status === ZONE_STATUS.ACTIVE) {
            onRegionChange?.(region.key);
            window.open(MCPZoneLink.toUrl({zoneId: region.id}), '_blank');
        }
    };

    return (
        <NavigationContainer>
            {regions.map(region => (
                <StyledRegionCard
                    key={region.key}
                    isActive={activeRegion === region.key}
                    disabled={region.disabled}
                    regionType={region.key}
                    onClick={() => handleRegionClick(region)}
                >
                    {region.key === 'dev' && (
                        <>
                            <BackgroundElement style={{top: '7px', left: '24px', fontSize: 38}}>
                                <IconDevBg1 />
                            </BackgroundElement>
                            <BackgroundElement style={{top: '4px', right: '4px', fontSize: 77}}>
                                <IconDevBg2 />
                            </BackgroundElement>
                        </>
                    )}
                    {region.key === 'ops' && (
                        <>
                            <BackgroundElement style={{top: '7px', left: '24px', fontSize: 38}}>
                                <IconOpsBg1 />
                            </BackgroundElement>
                            <BackgroundElement style={{top: '4px', right: '4px', fontSize: 77}}>
                                <IconOpsBg2 />
                            </BackgroundElement>
                        </>
                    )}
                    {region.key === 'test' && (
                        <>
                            <BackgroundElement style={{top: '7px', left: '24px', fontSize: 38}}>
                                <IconTestBg1 />
                            </BackgroundElement>
                            <BackgroundElement style={{top: '4px', right: '4px', fontSize: 77}}>
                                <IconTestBg2 />
                            </BackgroundElement>
                        </>
                    )}
                    <CardHeader>
                        <Flex>
                            <IconWrapper>{region.icon}</IconWrapper>
                            <CardTitle level={4}>
                                {region.title}{region.status === ZONE_STATUS.COMING ? '（敬请期待）' : ''}
                            </CardTitle>
                        </Flex>
                    </CardHeader>
                    <CardDescription>{region.description}</CardDescription>
                </StyledRegionCard>
            ))}
        </NavigationContainer>
    );
};

export default RegionNavigation;
