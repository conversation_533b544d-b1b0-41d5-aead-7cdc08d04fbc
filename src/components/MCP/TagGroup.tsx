import styled from '@emotion/styled';
import {Flex, Tooltip} from 'antd';
import {Tag, TagColor} from '@panda-design/components';
import {CSSProperties, ReactNode} from 'react';
import {IconTag} from '@/icons/mcp';

interface Label {
    id: number;
    label: string;
}
const TooltipTitleWrapper = styled.div`
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 4px 0;
`;

const COLOR_MAP: Record<string, CSSProperties> = {
    'light-purple': {
        backgroundColor: '#EBE6F9',
        color: '#8264DE',
    },
    'gray': {
        backgroundColor: '#E2E8F0',
        color: '#4B6C9F',
    },
};

interface Props {
    prefix?: ReactNode;
    color?: TagColor;
    labels: Label[];
    maxNum?: number;
    style?: CSSProperties;
    gap?: number;
}

const TagGroup = ({prefix = <IconTag />, labels, maxNum = 4, color = 'info', style, gap = 8}: Props) => {
    const colorStyle = COLOR_MAP[color];
    const innerColor = COLOR_MAP[color] ? undefined : color;
    return (
        <Flex style={{zIndex: 1, gap, height: 22, ...style}} align="center">
            {prefix}
            {
                labels.length > 0 ? labels.slice(0, maxNum).map(label => {
                    return (
                        <Tag
                            type="flat"
                            color={innerColor}
                            key={label.id}
                            style={{
                                margin: 0,
                                flexShrink: 1,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                ...colorStyle,
                            }}
                        >
                            {label.label}
                        </Tag>
                    );
                }) : (
                    <Tag
                        type="flat"
                        color={innerColor}
                        style={{
                            margin: 0,
                            ...colorStyle,
                        }}
                    >
                        暂无标签
                    </Tag>
                )
            }
            {
                labels.length > maxNum && (
                    <Tooltip
                        title={
                            <TooltipTitleWrapper>
                                {labels.slice(maxNum).map(label => {
                                    return (
                                        <Tag
                                            type="flat"
                                            color={innerColor}
                                            key={label.id}
                                            style={{
                                                margin: 0,
                                                ...colorStyle,
                                            }}
                                        >
                                            {label.label}
                                        </Tag>
                                    );
                                })}
                            </TooltipTitleWrapper>
                        }
                    >
                        <Tag
                            type="flat"
                            color={innerColor}
                            style={{
                                margin: 0,
                                ...colorStyle,
                            }}
                        >
                            +{labels.length - maxNum}
                        </Tag>
                    </Tooltip>
                )
            }
        </Flex>
    );
};

export default TagGroup;

