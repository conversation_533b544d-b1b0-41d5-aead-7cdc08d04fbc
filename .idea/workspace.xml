<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="96d7ac95-c7db-443e-a2d4-b06b4068ed31" name="Changes" comment="dev-partner-841 [Bug] 【广场新建专区】空间卡片展示问题123">
      <change beforePath="$PROJECT_DIR$/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/MCP/BaseMCPCard/BaseMCPCard.styles.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/MCP/BaseMCPCard/index.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/MCP/BaseMCPCard/styles.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/MCP/MCPServerCard/MCPServerCard.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/MCP/MCPServerCard/MCPServerCard.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="bugfix-lc" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="31BRu1SNliakUcJiGUo7azi8nUI" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager.252": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "feature-mcp",
    "last_opened_file_path": "/Users/<USER>/augment/devops-ai/comate-stack-fe",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.standard": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.standard": "",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "yarn",
    "settings.editor.selected.configurable": "settings.javascript.linters.eslint",
    "ts.external.directory.path": "/Users/<USER>/augment/devops-ai/comate-stack-fe/node_modules/typescript/lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e03c56caf84a-JavaScript-WS-252.23892.411" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="96d7ac95-c7db-443e-a2d4-b06b4068ed31" name="Changes" comment="" />
      <created>1754995697986</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754995697986</updated>
      <workItem from="1754995699244" duration="8319000" />
      <workItem from="1755083192230" duration="36665000" />
    </task>
    <task id="LOCAL-00001" summary="dev-partner-540 [Task] 【前端】【数字员工三期】AI Tools首页建设">
      <option name="closed" value="true" />
      <created>1755003342815</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755003342815</updated>
    </task>
    <task id="LOCAL-00002" summary="dev-partner-540 [Task] 【前端】【数字员工三期】AI Tools首页建设">
      <option name="closed" value="true" />
      <created>1755003439944</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755003439944</updated>
    </task>
    <task id="LOCAL-00003" summary="dev-partner-540 [Task] 【前端】【数字员工三期】AI Tools首页建设">
      <option name="closed" value="true" />
      <created>1755004687052</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755004687052</updated>
    </task>
    <task id="LOCAL-00004" summary="dev-partner-540 [Task] 【前端】【数字员工三期】AI Tools首页建设">
      <option name="closed" value="true" />
      <created>1755004763189</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755004763189</updated>
    </task>
    <task id="LOCAL-00005" summary="dev-partner-812 新增个人环境">
      <option name="closed" value="true" />
      <created>1755082459617</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755082459617</updated>
    </task>
    <task id="LOCAL-00006" summary="dev-partner-760 [Task] 【前端】【数字员工三期】【MCP】MCP卡片展示调整">
      <option name="closed" value="true" />
      <created>1755096627101</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755096627101</updated>
    </task>
    <task id="LOCAL-00007" summary="dev-partner-734 [Story] 【数字员工三期】【MCP】广场首页专区入口、筛选及排序形式调整">
      <option name="closed" value="true" />
      <created>1755137996311</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755137996311</updated>
    </task>
    <task id="LOCAL-00008" summary="dev-partner-732 [Story] 【数字员工三期】【MCP】MCP卡片展示调整">
      <option name="closed" value="true" />
      <created>1755176104392</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755176104392</updated>
    </task>
    <task id="LOCAL-00009" summary="dev-partner-732 [Story] 【数字员工三期】【MCP】MCP卡片展示调整&#10;&#10;Change-Id: I0f14e677ea3460fb8f87a8e887270ec5ca247789">
      <option name="closed" value="true" />
      <created>1755176231434</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755176231434</updated>
    </task>
    <task id="LOCAL-00010" summary="dev-partner-732 [Story] 【数字员工三期】【MCP】MCP卡片展示调整&#10;&#10;Change-Id: I0f14e677ea3460fb8f87a8e887270ec5ca247789">
      <option name="closed" value="true" />
      <created>1755176385211</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755176385211</updated>
    </task>
    <task id="LOCAL-00011" summary="dev-partner-732 [Story] 【数字员工三期】【MCP】MCP卡片展示调整&#10;&#10;Change-Id: I0f14e677ea3460fb8f87a8e887270ec5ca247789">
      <option name="closed" value="true" />
      <created>1755176426070</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1755176426070</updated>
    </task>
    <task id="LOCAL-00012" summary="dev-partner-732 [Story] 【数字员工三期】【MCP】MCP卡片展示调整">
      <option name="closed" value="true" />
      <created>1755177955616</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1755177955616</updated>
    </task>
    <task id="LOCAL-00013" summary="dev-partner-732 [Story] 【数字员工三期】【MCP】MCP卡片展示调整&#10;&#10;Change-Id: If8fef92a329a294408b0818a37dd986d9589352b">
      <option name="closed" value="true" />
      <created>1755178108332</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1755178108332</updated>
    </task>
    <task id="LOCAL-00014" summary="dev-partner-756 [Task] 【前端】【数字员工三期】【MCP注册】标准MCP的类型分为Remote Local">
      <option name="closed" value="true" />
      <created>1755179631357</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1755179631357</updated>
    </task>
    <task id="LOCAL-00015" summary="dev-partner-736 [Task] 【前端】【数字员工三期】【MCP】广场首页专区入口、筛选及排序形式调整">
      <option name="closed" value="true" />
      <created>1755224904539</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1755224904539</updated>
    </task>
    <task id="LOCAL-00016" summary="dev-partner-736 [Task] 【前端】【数字员工三期】【MCP】广场首页专区入口、筛选及排序形式调整&#10;&#10;Change-Id: I990a913f056ace38489d3ddeadcb8d5d8dfe410e">
      <option name="closed" value="true" />
      <created>1755226094252</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1755226094252</updated>
    </task>
    <task id="LOCAL-00017" summary="dev-partner-736 [Task] 【前端】【数字员工三期】【MCP】广场首页专区入口、筛选及排序形式调整&#10;&#10;Change-Id: I990a913f056ace38489d3ddeadcb8d5d8dfe410e">
      <option name="closed" value="true" />
      <created>1755238275614</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1755238275615</updated>
    </task>
    <task id="LOCAL-00018" summary="dev-partner-842 [Bug] 【前端】【广场空间MCP卡片】基本信息路由跳转不正确">
      <option name="closed" value="true" />
      <created>1755240418614</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1755240418615</updated>
    </task>
    <task id="LOCAL-00019" summary="dev-partner-842 [Bug] 【前端】【广场空间MCP卡片】基本信息路由跳转不正确&#10;&#10;Change-Id: I0a70801bef82feeca15223108aabf2379a637e33">
      <option name="closed" value="true" />
      <created>1755240836955</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1755240836955</updated>
    </task>
    <task id="LOCAL-00020" summary="dev-partner-842 [Bug] 【前端】【广场空间MCP卡片】基本信息路由跳转不正确&#10;&#10;Change-Id: I0a70801bef82feeca15223108aabf2379a637e33">
      <option name="closed" value="true" />
      <created>1755240947628</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1755240947629</updated>
    </task>
    <task id="LOCAL-00021" summary="dev-partner-842 [Bug] 【前端】【广场空间MCP卡片】基本信息路由跳转不正确">
      <option name="closed" value="true" />
      <created>1755241199614</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1755241199614</updated>
    </task>
    <task id="LOCAL-00022" summary="dev-partner-567 [Task] 【前端】【数字员工三期】【VIS需求】支持云上百度认证方式">
      <option name="closed" value="true" />
      <created>1755244837623</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1755244837623</updated>
    </task>
    <task id="LOCAL-00023" summary="dev-partner-567 [Task] 【前端】【数字员工三期】【VIS需求】支持云上百度认证方式&#10;&#10;Change-Id: Ie1c52f1897d54af9fd5c366424ef0de49ddb2a49">
      <option name="closed" value="true" />
      <created>1755245385356</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1755245385356</updated>
    </task>
    <task id="LOCAL-00024" summary="dev-partner-841 [Bug] 【广场新建专区】空间卡片展示问题123">
      <option name="closed" value="true" />
      <created>1755252533180</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1755252533180</updated>
    </task>
    <option name="localTasksCounter" value="25" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="dev-partner-540 [Task] 【前端】【数字员工三期】AI Tools首页建设&#10;&#10;Change-Id: I0e65cdeaf11b3dc79bef75003b5002a78c3c3870" />
    <MESSAGE value="dev-partner-540 [Task] 【前端】【数字员工三期】AI Tools首页建设" />
    <MESSAGE value="dev-partner-812 新增个人环境" />
    <MESSAGE value="dev-partner-760 [Task] 【前端】【数字员工三期】【MCP】MCP卡片展示调整" />
    <MESSAGE value="dev-partner-734 [Story] 【数字员工三期】【MCP】广场首页专区入口、筛选及排序形式调整" />
    <MESSAGE value="dev-partner-732 [Story] 【数字员工三期】【MCP】MCP卡片展示调整&#10;&#10;Change-Id: I0f14e677ea3460fb8f87a8e887270ec5ca247789" />
    <MESSAGE value="dev-partner-732 [Story] 【数字员工三期】【MCP】MCP卡片展示调整" />
    <MESSAGE value="dev-partner-732 [Story] 【数字员工三期】【MCP】MCP卡片展示调整&#10;&#10;Change-Id: If8fef92a329a294408b0818a37dd986d9589352b" />
    <MESSAGE value="dev-partner-756 [Task] 【前端】【数字员工三期】【MCP注册】标准MCP的类型分为Remote Local" />
    <MESSAGE value="dev-partner-736 [Task] 【前端】【数字员工三期】【MCP】广场首页专区入口、筛选及排序形式调整" />
    <MESSAGE value="dev-partner-736 [Task] 【前端】【数字员工三期】【MCP】广场首页专区入口、筛选及排序形式调整&#10;&#10;Change-Id: I990a913f056ace38489d3ddeadcb8d5d8dfe410e" />
    <MESSAGE value="dev-partner-842 [Bug] 【前端】【广场空间MCP卡片】基本信息路由跳转不正确&#10;&#10;Change-Id: I0a70801bef82feeca15223108aabf2379a637e33" />
    <MESSAGE value="dev-partner-842 [Bug] 【前端】【广场空间MCP卡片】基本信息路由跳转不正确" />
    <MESSAGE value="dev-partner-567 [Task] 【前端】【数字员工三期】【VIS需求】支持云上百度认证方式" />
    <MESSAGE value="dev-partner-567 [Task] 【前端】【数字员工三期】【VIS需求】支持云上百度认证方式&#10;&#10;Change-Id: Ie1c52f1897d54af9fd5c366424ef0de49ddb2a49" />
    <MESSAGE value="dev-partner-841 [Bug] 【广场新建专区】空间卡片展示问题123" />
    <option name="LAST_COMMIT_MESSAGE" value="dev-partner-841 [Bug] 【广场新建专区】空间卡片展示问题123" />
  </component>
</project>