Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># See https://help.github.com/articles/ignoring-files/ for more about ignoring files.\n\n# dependencies\n/node_modules\n/.pnp\n.pnp.js\n\n# testing\n/coverage\n\n# production\n/build\n/output\n/.comate-f2c\n\n# misc\n.DS_Store\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision f52b950c154a3b0bf850582b3bd2300d8f460e54)
+++ b/.gitignore	(date 1755180979044)
@@ -23,3 +23,28 @@
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
+.idea/.gitignore
+.idea/comate-stack-fe.iml
+.idea/modules.xml
+.idea/vcs.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+/.augment/rules/mode.md
+/.idea/jsLinters/eslint.xml
+.idea/workspace.xml
+.idea/shelf/Changes.xml
+.idea/shelf/Changes1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10__Changes_.xml
+.idea/shelf/Changes/shelved.patch
+.idea/shelf/Changes1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21__Changes_1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_15_21_\[Changes]1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/cardBg.png
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_14_19_16_\[Changes]/shelved.patch
Index: src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* eslint-disable max-lines */\nimport styled from '@emotion/styled';\nimport {Flex, Typography} from 'antd';\nimport {ReactNode} from 'react';\nimport {\n    IconArrowRight,\n    IconDev,\n    IconOps,\n    IconTest,\n    IconDevBg1,\n    IconDevBg2,\n    IconOpsBg1,\n    IconOpsBg2,\n    IconTestBg1,\n    IconTestBg2,\n} from '@/icons/mcp';\nimport {MCPZoneLink} from '@/links/mcp';\n\nconst NavigationContainer = styled(Flex)`\n    margin-top: 20px;\n    gap: 12px;\n`;\n\nconst RegionCard = styled.div<{ isActive?: boolean, disabled?: boolean, regionType?: string }>`\n    flex: 1;\n    padding: 24px 20px;\n    border: 1px solid #D9D9D9;\n    border-radius: 10px;\n    background: ${props => {\n        switch (props.regionType) {\n            case 'dev':\n                return 'linear-gradient(329.12deg, #E7F1FF -8.74%, #FFFFFF 92.24%)';\n            case 'ops':\n                return 'linear-gradient(328.39deg, #DEF3FF -8.86%, #FFFFFF 89.14%)';\n            case 'test':\n                return 'linear-gradient(328.02deg, #F0ECFE -8.92%, #FFFFFF 92.24%)';\n            default:\n                return 'linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%)';\n        }\n    }};\n    transition: all 0.3s ease;\n    position: relative;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n\n    &:hover {\n        ${props =>\n        !props.disabled\n            && `\n            border-color: #0083FF;\n            transform: translateY(-2px);\n            box-shadow: 0px 5px 16px 0px #00000021;\n        `}\n    }\n`;\n\nconst CardHeader = styled(Flex)`\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 12px;\n    position: relative;\n    z-index: 2;\n`;\n\nconst IconWrapper = styled.div`\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-right: 12px;\n    svg {\n        font-size: 20px;\n    }\n`;\n\nconst CardTitle = styled(Typography.Title)`\n    margin: 0 !important;\n    font-size: 16px !important;\n    font-weight: 600 !important;\n    line-height: 28px !important;\n`;\n\nconst CardDescription = styled(Typography.Text)`\n    font-size: 12px;\n    line-height: 20px;\n    color: #5C5C5C;\n    position: relative;\n    z-index: 2;\n`;\n\nconst HoverActionContainer = styled(Flex)`\n    align-items: center;\n    gap: 8px;\n`;\n\nconst HoverText = styled.span`\n    color: #0083FF;\n    font-size: 12px;\n    line-height: 20px;\n    opacity: 0;\n    transition: opacity 0.3s ease;\n    white-space: nowrap;\n`;\n\nconst ArrowIcon = styled(IconArrowRight)`\n    color: #8c8c8c;\n    transition: all 0.3s ease;\n    font-size: 16px;\n\n    svg {\n        transition: all 0.3s ease;\n    }\n\n    path {\n        fill: #8c8c8c !important;\n        transition: fill 0.3s ease;\n    }\n`;\n\nconst BackgroundElement = styled.div`\n    position: absolute;\n    z-index: 1;\n    pointer-events: none;\n`;\n\nconst StyledRegionCard = styled(RegionCard)`\n    cursor: pointer;\n    &:hover {\n        ${HoverText} {\n            opacity: 1;\n        }\n\n        ${ArrowIcon} {\n            color: #0083FF;\n            path {\n                fill: #0083FF !important;\n            }\n        }\n    }\n`;\n\ninterface RegionItem {\n    key: string;\n    title: string;\n    description: string;\n    icon: ReactNode;\n    status?: 'active' | 'coming';\n    disabled?: boolean;\n    onClick?: () => void;\n    // 用id来跳转\n    id: string;\n}\n\ninterface Props {\n    activeRegion?: string;\n    onRegionChange?: (region: string) => void;\n}\n\nconst RegionNavigation = ({activeRegion = 'dev', onRegionChange}: Props) => {\n    const regions: RegionItem[] = [\n        {\n            key: 'dev',\n            title: '开发专区',\n            description:\n                '面向研发场景提供丰富且高质量的MCP工具，覆盖DevOps 研发流程及AI应用开发的各环节，助力研发提效。 ',\n            icon: <IconDev />,\n            status: 'active',\n            disabled: false,\n            id: '5',\n        },\n        {\n            key: 'ops',\n            title: '运维专区',\n            description:\n                '面向运维的MCP中心，汇聚各类高质量工具，在通用运维、云运维、业务运维等场景助力快速定位故障、分析根因、提升排障效率。',\n            icon: <IconOps />,\n            status: 'active',\n            disabled: false,\n            id: '3',\n        },\n        {\n            key: 'test',\n            title: '测试专区',\n            description:\n                '由各QA团队共建共享常用的测试工具、测试平台MCP Server，加速测试流程智能化改造。',\n            icon: <IconTest />,\n            status: 'active',\n            disabled: false,\n            id: '1',\n        },\n    ];\n\n    const handleRegionClick = (region: RegionItem) => {\n        if (!region.disabled && region.status === 'active') {\n            onRegionChange?.(region.key);\n            window.open(MCPZoneLink.toUrl({zoneId: region.id}), '_blank');\n        }\n    };\n\n    return (\n        <NavigationContainer>\n            {regions.map(region => (\n                <StyledRegionCard\n                    key={region.key}\n                    isActive={activeRegion === region.key}\n                    disabled={region.disabled}\n                    regionType={region.key}\n                    onClick={() => handleRegionClick(region)}\n                >\n                    {region.key === 'dev' && (\n                        <>\n                            <BackgroundElement style={{top: '7px', left: '24px', fontSize: 38}}>\n                                <IconDevBg1 />\n                            </BackgroundElement>\n                            <BackgroundElement style={{top: '4px', right: '4px', fontSize: 77}}>\n                                <IconDevBg2 />\n                            </BackgroundElement>\n                        </>\n                    )}\n                    {region.key === 'ops' && (\n                        <>\n                            <BackgroundElement style={{top: '7px', left: '24px', fontSize: 38}}>\n                                <IconOpsBg1 />\n                            </BackgroundElement>\n                            <BackgroundElement style={{top: '4px', right: '4px', fontSize: 77}}>\n                                <IconOpsBg2 />\n                            </BackgroundElement>\n                        </>\n                    )}\n                    {region.key === 'test' && (\n                        <>\n                            <BackgroundElement style={{top: '7px', left: '24px', fontSize: 38}}>\n                                <IconTestBg1 />\n                            </BackgroundElement>\n                            <BackgroundElement style={{top: '4px', right: '4px', fontSize: 77}}>\n                                <IconTestBg2 />\n                            </BackgroundElement>\n                        </>\n                    )}\n                    <CardHeader>\n                        <Flex>\n                            <IconWrapper>{region.icon}</IconWrapper>\n                            <CardTitle level={4}>{region.title}{region.status === 'coming' ? '（敬请期待）' : ''}</CardTitle>\n                        </Flex>\n                        <HoverActionContainer>\n                            <HoverText>进入专区</HoverText>\n                            <ArrowIcon />\n                        </HoverActionContainer>\n                    </CardHeader>\n                    <CardDescription>{region.description}</CardDescription>\n                </StyledRegionCard>\n            ))}\n        </NavigationContainer>\n    );\n};\n\nexport default RegionNavigation;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx b/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx
--- a/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx	(revision f52b950c154a3b0bf850582b3bd2300d8f460e54)
+++ b/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx	(date 1755225624059)
@@ -23,7 +23,7 @@
 
 const RegionCard = styled.div<{ isActive?: boolean, disabled?: boolean, regionType?: string }>`
     flex: 1;
-    padding: 24px 20px;
+    padding: 20px 14px 26px;
     border: 1px solid #D9D9D9;
     border-radius: 10px;
     background: ${props => {
@@ -56,7 +56,7 @@
 const CardHeader = styled(Flex)`
     align-items: center;
     justify-content: space-between;
-    margin-bottom: 12px;
+    margin-bottom: 14px;
     position: relative;
     z-index: 2;
 `;
@@ -65,32 +65,26 @@
     display: flex;
     align-items: center;
     justify-content: center;
-    margin-right: 12px;
+    margin-right: 8px;
     svg {
-        font-size: 20px;
+        font-size: 26px;
     }
 `;
 
 const CardTitle = styled(Typography.Title)`
-    margin: 0 !important;
     font-size: 16px !important;
     font-weight: 600 !important;
     line-height: 28px !important;
 `;
 
 const CardDescription = styled(Typography.Text)`
-    font-size: 12px;
-    line-height: 20px;
-    color: #5C5C5C;
+    font-size: 14px;
+    line-height: 22px;
+    color: #545454;
     position: relative;
     z-index: 2;
 `;
 
-const HoverActionContainer = styled(Flex)`
-    align-items: center;
-    gap: 8px;
-`;
-
 const HoverText = styled.span`
     color: #0083FF;
     font-size: 12px;
@@ -240,10 +234,6 @@
                             <IconWrapper>{region.icon}</IconWrapper>
                             <CardTitle level={4}>{region.title}{region.status === 'coming' ? '（敬请期待）' : ''}</CardTitle>
                         </Flex>
-                        <HoverActionContainer>
-                            <HoverText>进入专区</HoverText>
-                            <ArrowIcon />
-                        </HoverActionContainer>
                     </CardHeader>
                     <CardDescription>{region.description}</CardDescription>
                 </StyledRegionCard>
