Index: src/constants/colors/myColors.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// 以下颜色是自定义颜色，没有限制，可以自行添加\n// 如果有非常相近的颜色，可以和视觉同学确认下能不能统一\n// 在添加颜色之前，先确认一下 token 里面有没有对应的颜色，比如 colorPrimaryBg 之类的\n// token 对应的规范可以看 https://panda-design-team.github.io/\nexport const myColors = {\n    aiBg: '#8c66ff1a',\n    steelBlueLight: '#e9ebef',\n    muted: '#f8fafc',\n    textBlue: '#60a5fa',\n    darkGray: '#4b5563',\n    lightGray: '#9ca3af',\n    whiteTransparent: 'rgba(255, 255, 255, 0.5)',\n    blueGray: '#4b6c9f',\n    lightBlueGray: '#4b6c9f26',\n    primaryTransparent40: '#0080ff66',\n    lightBlueTransparent15: '#8dc9fe26',\n    lightBlueTransparent30: '#8dc9fe4d',\n    legacyPrimary: '#0080ff',\n    primaryFocus: '#cce5ff',\n    primaryBg: '#e5f2ff',\n    primaryBgTransparent: '#e5f2ff33',\n    purple: '#8862e6',\n    purpleTransparent: '#8862e626',\n    success: '#00aa5b',\n    successTransparent: '#00cc6d1a',\n    yellow: '#f58300',\n    yellowTransparent: '#f583001a',\n    infoTransparent15: '#317ff526',\n    infoTransparent20: '#317ff533',\n    // 从 icode 迁入\n    aiGradientBg1: '#f5faff',\n    aiGradientBg2: '#fff3e8',\n    infoShadowColor: '#108cee26',\n    // 一部分透明色\n    blackTransparent60: 'rgba(0, 0, 0, 60%)',\n    blackTransparent30: 'rgba(0, 0, 0, 30%)',\n    blackTransparent15: 'rgba(0, 0, 0, 15%)',\n    blackTransparent10: 'rgba(0, 0, 0, 10%)',\n    whiteTransparent60: 'rgba(255, 255, 255, 60%)',\n    redTransparent50: 'rgba(249, 213, 213, 50%)',\n    blueTransparent60: 'rgba(191, 216, 246, 60%)',\n    primaryLegacyTransparent5: '#108cee0d',\n};\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/constants/colors/myColors.ts b/src/constants/colors/myColors.ts
--- a/src/constants/colors/myColors.ts	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/constants/colors/myColors.ts	(date 1755094697756)
@@ -24,6 +24,10 @@
     success: '#00aa5b',
     successTransparent: '#00cc6d1a',
     yellow: '#f58300',
+    // MCP评分颜色
+    ratingYellow: '#f58300',
+    ratingOrange: '#ff8c00',
+    ratingRed: '#e62c4b',
     yellowTransparent: '#f583001a',
     infoTransparent15: '#317ff526',
     infoTransparent20: '#317ff533',
Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># See https://help.github.com/articles/ignoring-files/ for more about ignoring files.\n\n# dependencies\n/node_modules\n/.pnp\n.pnp.js\n\n# testing\n/coverage\n\n# production\n/build\n/output\n/.comate-f2c\n\n# misc\n.DS_Store\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/.gitignore	(date 1755094697748)
@@ -23,3 +23,28 @@
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
+.idea/.gitignore
+.idea/comate-stack-fe.iml
+.idea/modules.xml
+.idea/vcs.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+/.augment/rules/mode.md
+/.idea/jsLinters/eslint.xml
+.idea/.gitignore
+.idea/comate-stack-fe.iml
+.idea/modules.xml
+.idea/vcs.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+/.augment/rules/mode.md
+/.idea/jsLinters/eslint.xml
+.idea/workspace.xml
+.idea/shelf/Changes.xml
+.idea/shelf/Changes1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10__Changes_.xml
+.idea/shelf/Changes/shelved.patch
+.idea/shelf/Changes1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10_\[Changes]/shelved.patch
Index: src/icons/mcp/index.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import type { FC, SVGProps } from \"react\";\nimport { createIcon } from \"@panda-design/components\";\nimport SendActived from \"./SendActived\";\nimport AiTools1 from \"./AiTools1\";\nimport AiTools2 from \"./AiTools2\";\nimport AiTools3 from \"./AiTools3\";\nimport AiTools4 from \"./AiTools4\";\nimport Alert from \"./Alert\";\nimport ArrowRight from \"./ArrowRight\";\nimport Copy from \"./Copy\";\nimport Debug from \"./Debug\";\nimport Delete from \"./Delete\";\nimport Detail from \"./Detail\";\nimport Elipsis from \"./Elipsis\";\nimport ExitFullscreen from \"./ExitFullscreen\";\nimport Eye from \"./Eye\";\nimport Fullscreen from \"./Fullscreen\";\nimport Import from \"./Import\";\nimport LeftOutlined from \"./LeftOutlined\";\nimport LightMySpcae from \"./LightMySpcae\";\nimport LightPlayground from \"./LightPlayground\";\nimport List from \"./List\";\nimport Local from \"./Local\";\nimport LocalMcp from \"./LocalMcp\";\nimport McpAvatar from \"./McpAvatar\";\nimport MySpcae from \"./MySpcae\";\nimport OffcialExample from \"./OffcialExample\";\nimport Organization from \"./Organization\";\nimport Params from \"./Params\";\nimport Playground from \"./Playground\";\nimport PlaygroundConfig from \"./PlaygroundConfig\";\nimport Refresh from \"./Refresh\";\nimport Remote from \"./Remote\";\nimport RemoteMcp from \"./RemoteMcp\";\nimport Result from \"./Result\";\nimport RightArrow from \"./RightArrow\";\nimport Send from \"./Send\";\nimport Setting from \"./Setting\";\nimport ShowMore from \"./ShowMore\";\nimport SortAsc from \"./SortAsc\";\nimport SortDesc from \"./SortDesc\";\nimport Sse from \"./Sse\";\nimport Standard from \"./Standard\";\nimport Stdio from \"./Stdio\";\nimport StdioMcp from \"./StdioMcp\";\nimport Step01 from \"./Step01\";\nimport Step02 from \"./Step02\";\nimport Step03 from \"./Step03\";\nimport Step04 from \"./Step04\";\nimport StopGenerate from \"./StopGenerate\";\nimport Subscribe from \"./Subscribe\";\nimport SubscribeFilled from \"./SubscribeFilled\";\nimport Subtract from \"./Subtract\";\nimport Tag from \"./Tag\";\nimport Tool from \"./Tool\";\nimport Unfold from \"./Unfold\";\n\nexport const IconSendActived = createIcon(SendActived);\nexport const IconAiTools1 = createIcon(AiTools1);\nexport const IconAiTools2 = createIcon(AiTools2);\nexport const IconAiTools3 = createIcon(AiTools3);\nexport const IconAiTools4 = createIcon(AiTools4);\nexport const IconAlert = createIcon(Alert);\nexport const IconArrowRight = createIcon(ArrowRight);\nexport const IconCopy = createIcon(Copy);\nexport const IconDebug = createIcon(Debug);\nexport const IconDelete = createIcon(Delete);\nexport const IconDetail = createIcon(Detail);\nexport const IconElipsis = createIcon(Elipsis);\nexport const IconExitFullscreen = createIcon(ExitFullscreen);\nexport const IconEye = createIcon(Eye);\nexport const IconFullscreen = createIcon(Fullscreen);\nexport const IconImport = createIcon(Import);\nexport const IconLeftOutlined = createIcon(LeftOutlined);\nexport const IconLightMySpcae = createIcon(LightMySpcae);\nexport const IconLightPlayground = createIcon(LightPlayground);\nexport const IconList = createIcon(List);\nexport const IconLocal = createIcon(Local);\nexport const IconLocalMcp = createIcon(LocalMcp);\nexport const IconMcpAvatar = createIcon(McpAvatar);\nexport const IconMySpcae = createIcon(MySpcae);\nexport const IconOffcialExample = createIcon(OffcialExample);\nexport const IconOrganization = createIcon(Organization);\nexport const IconParams = createIcon(Params);\nexport const IconPlayground = createIcon(Playground);\nexport const IconPlaygroundConfig = createIcon(PlaygroundConfig);\nexport const IconRefresh = createIcon(Refresh);\nexport const IconRemote = createIcon(Remote);\nexport const IconRemoteMcp = createIcon(RemoteMcp);\nexport const IconResult = createIcon(Result);\nexport const IconRightArrow = createIcon(RightArrow);\nexport const IconSend = createIcon(Send);\nexport const IconSetting = createIcon(Setting);\nexport const IconShowMore = createIcon(ShowMore);\nexport const IconSortAsc = createIcon(SortAsc);\nexport const IconSortDesc = createIcon(SortDesc);\nexport const IconSse = createIcon(Sse);\nexport const IconStandard = createIcon(Standard);\nexport const IconStdio = createIcon(Stdio);\nexport const IconStdioMcp = createIcon(StdioMcp);\nexport const IconStep01 = createIcon(Step01);\nexport const IconStep02 = createIcon(Step02);\nexport const IconStep03 = createIcon(Step03);\nexport const IconStep04 = createIcon(Step04);\nexport const IconStopGenerate = createIcon(StopGenerate);\nexport const IconSubscribe = createIcon(Subscribe);\nexport const IconSubscribeFilled = createIcon(SubscribeFilled);\nexport const IconSubtract = createIcon(Subtract);\nexport const IconTag = createIcon(Tag);\nexport const IconTool = createIcon(Tool);\nexport const IconUnfold = createIcon(Unfold);\n\nexport const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {\n    SendActived: IconSendActived,\n    aiTools1: IconAiTools1,\n    aiTools2: IconAiTools2,\n    aiTools3: IconAiTools3,\n    aiTools4: IconAiTools4,\n    alert: IconAlert,\n    arrowRight: IconArrowRight,\n    copy: IconCopy,\n    debug: IconDebug,\n    delete: IconDelete,\n    detail: IconDetail,\n    elipsis: IconElipsis,\n    exitFullscreen: IconExitFullscreen,\n    eye: IconEye,\n    fullscreen: IconFullscreen,\n    import: IconImport,\n    leftOutlined: IconLeftOutlined,\n    lightMySpcae: IconLightMySpcae,\n    lightPlayground: IconLightPlayground,\n    list: IconList,\n    local: IconLocal,\n    localMCP: IconLocalMcp,\n    mcpAvatar: IconMcpAvatar,\n    mySpcae: IconMySpcae,\n    offcialExample: IconOffcialExample,\n    organization: IconOrganization,\n    params: IconParams,\n    playground: IconPlayground,\n    playgroundConfig: IconPlaygroundConfig,\n    refresh: IconRefresh,\n    remote: IconRemote,\n    remoteMCP: IconRemoteMcp,\n    result: IconResult,\n    rightArrow: IconRightArrow,\n    send: IconSend,\n    setting: IconSetting,\n    showMore: IconShowMore,\n    sortAsc: IconSortAsc,\n    sortDesc: IconSortDesc,\n    sse: IconSse,\n    standard: IconStandard,\n    stdio: IconStdio,\n    stdioMCP: IconStdioMcp,\n    step01: IconStep01,\n    step02: IconStep02,\n    step03: IconStep03,\n    step04: IconStep04,\n    stopGenerate: IconStopGenerate,\n    subscribe: IconSubscribe,\n    subscribeFilled: IconSubscribeFilled,\n    subtract: IconSubtract,\n    tag: IconTag,\n    tool: IconTool,\n    unfold: IconUnfold,\n};\n\nexport default iconsMap;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/index.ts b/src/icons/mcp/index.ts
--- a/src/icons/mcp/index.ts	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/icons/mcp/index.ts	(date 1755094697761)
@@ -7,6 +7,10 @@
 import AiTools4 from "./AiTools4";
 import Alert from "./Alert";
 import ArrowRight from "./ArrowRight";
+import ArrowRight1 from "./ArrowRight1";
+import Case from "./Case";
+import Code from "./Code";
+import Comment from "./Comment";
 import Copy from "./Copy";
 import Debug from "./Debug";
 import Delete from "./Delete";
@@ -25,6 +29,7 @@
 import McpAvatar from "./McpAvatar";
 import MySpcae from "./MySpcae";
 import OffcialExample from "./OffcialExample";
+import Ops from "./Ops";
 import Organization from "./Organization";
 import Params from "./Params";
 import Playground from "./Playground";
@@ -52,8 +57,10 @@
 import SubscribeFilled from "./SubscribeFilled";
 import Subtract from "./Subtract";
 import Tag from "./Tag";
+import Test from "./Test";
 import Tool from "./Tool";
 import Unfold from "./Unfold";
+import SubscribeBlue from "./SubscribeBlue";
 
 export const IconSendActived = createIcon(SendActived);
 export const IconAiTools1 = createIcon(AiTools1);
@@ -62,6 +69,10 @@
 export const IconAiTools4 = createIcon(AiTools4);
 export const IconAlert = createIcon(Alert);
 export const IconArrowRight = createIcon(ArrowRight);
+export const IconArrowRight1 = createIcon(ArrowRight1);
+export const IconCase = createIcon(Case);
+export const IconCode = createIcon(Code);
+export const IconComment = createIcon(Comment);
 export const IconCopy = createIcon(Copy);
 export const IconDebug = createIcon(Debug);
 export const IconDelete = createIcon(Delete);
@@ -80,6 +91,7 @@
 export const IconMcpAvatar = createIcon(McpAvatar);
 export const IconMySpcae = createIcon(MySpcae);
 export const IconOffcialExample = createIcon(OffcialExample);
+export const IconOps = createIcon(Ops);
 export const IconOrganization = createIcon(Organization);
 export const IconParams = createIcon(Params);
 export const IconPlayground = createIcon(Playground);
@@ -104,9 +116,11 @@
 export const IconStep04 = createIcon(Step04);
 export const IconStopGenerate = createIcon(StopGenerate);
 export const IconSubscribe = createIcon(Subscribe);
+export const IconSubscribeBlue = createIcon(SubscribeBlue);
 export const IconSubscribeFilled = createIcon(SubscribeFilled);
 export const IconSubtract = createIcon(Subtract);
 export const IconTag = createIcon(Tag);
+export const IconTest = createIcon(Test);
 export const IconTool = createIcon(Tool);
 export const IconUnfold = createIcon(Unfold);
 
@@ -118,6 +132,10 @@
     aiTools4: IconAiTools4,
     alert: IconAlert,
     arrowRight: IconArrowRight,
+    arrowRight1: IconArrowRight1,
+    case: IconCase,
+    code: IconCode,
+    comment: IconComment,
     copy: IconCopy,
     debug: IconDebug,
     delete: IconDelete,
@@ -136,6 +154,7 @@
     mcpAvatar: IconMcpAvatar,
     mySpcae: IconMySpcae,
     offcialExample: IconOffcialExample,
+    ops: IconOps,
     organization: IconOrganization,
     params: IconParams,
     playground: IconPlayground,
@@ -160,9 +179,11 @@
     step04: IconStep04,
     stopGenerate: IconStopGenerate,
     subscribe: IconSubscribe,
+    subscribeBlue: IconSubscribeBlue,
     subscribeFilled: IconSubscribeFilled,
     subtract: IconSubtract,
     tag: IconTag,
+    test: IconTest,
     tool: IconTool,
     unfold: IconUnfold,
 };
Index: src/comatestack/MCP/MCPSquare/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {Flex, Form} from 'antd';\nimport {useCallback, useState} from 'react';\nimport {useDebouncedCallback} from 'huse';\nimport SquireFilter, {FilterValues, TabValues} from './SquireFilter';\nimport SquirePanel from './SquirePanel';\n\nconst Container = styled(Flex)`\n    width: 100%;\n    height: calc(100vh - 48px);\n    padding: 0 20px 16px;\n    overflow: auto;\n`;\n\nconst initialTabFormValue = {tab: 'all'};\nconst initialFilterFormValue: FilterValues = {\n    serverSourceType: 'all',\n    serverProtocolType: 'all',\n    labels: [-2],\n    viewOrder: 'DESC',\n};\nconst initialSearchParams = {\n    ...initialTabFormValue,\n    ...initialFilterFormValue,\n};\n\nconst MCPSquare = () => {\n    const [searchParams, setSearchParams] = useState<FilterValues & TabValues>(initialSearchParams);\n    const onFormChange = useCallback(\n        (formName: string, info: {changedFields: any}) => {\n            // 这里响应的是用户的修改，只取第一个就可以了。用户也没法一次改多个\n            const changedParams: Partial<FilterValues & TabValues> = {\n                [info.changedFields[0].name]: info.changedFields[0].value,\n            };\n            // 这俩互斥\n            if (info.changedFields[0].name[0] === 'viewOrder') {\n                changedParams.publishOrder = undefined;\n            } else if (info.changedFields[0].name[0] === 'publishOrder') {\n                changedParams.viewOrder = undefined;\n            }\n            setSearchParams(params => {\n                return {\n                    ...params,\n                    ...changedParams,\n                };\n            });\n        },\n        [setSearchParams]\n    );\n    const onFormChangeWithDebounce = useDebouncedCallback(onFormChange, 200);\n    return (\n        <Container vertical gap={16}>\n            <Form.Provider onFormChange={onFormChangeWithDebounce}>\n                <SquireFilter\n                    initialTabFormValue={initialTabFormValue}\n                    initialFilterFormValue={initialFilterFormValue}\n                />\n            </Form.Provider>\n            <SquirePanel searchParams={searchParams} />\n        </Container>\n    );\n};\n\nexport default MCPSquare;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/index.tsx b/src/comatestack/MCP/MCPSquare/index.tsx
--- a/src/comatestack/MCP/MCPSquare/index.tsx	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/comatestack/MCP/MCPSquare/index.tsx	(date 1755094697752)
@@ -4,6 +4,7 @@
 import {useDebouncedCallback} from 'huse';
 import SquireFilter, {FilterValues, TabValues} from './SquireFilter';
 import SquirePanel from './SquirePanel';
+import RegionNavigation from './RegionNavigation';
 
 const Container = styled(Flex)`
     width: 100%;
@@ -50,6 +51,7 @@
     const onFormChangeWithDebounce = useDebouncedCallback(onFormChange, 200);
     return (
         <Container vertical gap={16}>
+            <RegionNavigation />
             <Form.Provider onFormChange={onFormChangeWithDebounce}>
                 <SquireFilter
                     initialTabFormValue={initialTabFormValue}
Index: src/components/MCP/MCPSubscribeButton/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Button, ButtonProps, message} from '@panda-design/components';\nimport {MouseEvent, useCallback, CSSProperties, useEffect, useMemo} from 'react';\nimport {Popconfirm} from 'antd';\nimport {useBoolean} from 'huse';\nimport {loadAppListForMCPServer, setMCPSubscribe, useAppListForMCPServer} from '@/regions/mcp/mcpServer';\nimport {IconSubscribe} from '@/icons/mcp';\nimport {openMCPApplicationModal} from '@/regions/mcp/mcpApplication';\nimport {ApplicationBase} from '@/types/mcp/mcp';\nimport {IconSubscribeFilled} from '../../../icons/mcp/index';\nimport {useMCPWorkspaceId} from '../hooks';\n\ninterface Props {\n    showText?: boolean;\n    showCount?: boolean;\n    id: number;\n    workspaceId: number;\n    enableGlobalParams?: boolean;\n    refresh?: () => void;\n    style?: CSSProperties;\n    size?: ButtonProps['size'];\n}\n\nexport const MCPSubscribeButton = ({id, workspaceId, style, showText, showCount, size, enableGlobalParams}: Props) => {\n    const [open, {on, off}] = useBoolean(false);\n    const spaceId = useMCPWorkspaceId();\n    const appList = useAppListForMCPServer(id);\n\n    const apps = useMemo<ApplicationBase[]>(\n        () => {\n            const data = appList?.reduce((acc, cur) => {\n                return [...acc, ...(spaceId && cur.workspaceId !== spaceId ? [] : cur.applications)];\n            }, []);\n            return data;\n        },\n        [appList, spaceId]\n    );\n    const subscribedNumber = useMemo(\n        () => (apps ?? []).filter(app => app.ifSub).length,\n        [apps]\n    );\n    const isSubscribed = subscribedNumber > 0;\n\n    const handleClick = useCallback(\n        async (e: MouseEvent) => {\n            e.stopPropagation();\n            try {\n                if (apps?.length) {\n                    setMCPSubscribe({\n                        serverId: id,\n                        workspaceId,\n                        enableGlobalParams: enableGlobalParams ?? false,\n                        onSuccess: () => loadAppListForMCPServer({mcpServerId: id}),\n                    });\n                }\n                else {\n                    on();\n                }\n            }\n            catch (e) {\n                message.error('服务器开小差了，请稍后重试');\n            }\n        },\n        [apps, id, workspaceId, enableGlobalParams, on]\n    );\n\n    const handleCancel = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            off();\n        },\n        [off]\n    );\n\n    const handleOk = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            off();\n            openMCPApplicationModal();\n        },\n        [off]\n    );\n\n\n    useEffect(\n        () => {\n            if (!spaceId && !apps?.length) {\n                loadAppListForMCPServer({mcpServerId: id});\n            }\n            if (spaceId) {\n                loadAppListForMCPServer({mcpServerId: id});\n            }\n        },\n        [apps?.length, id, spaceId]\n    );\n\n    return (\n        <Popconfirm\n            title=\"\"\n            description=\"您还没有可用应用，请先创建后订阅\"\n            open={open}\n            onConfirm={handleOk}\n            onCancel={handleCancel}\n            okText=\"立即创建\"\n            cancelText=\"稍后再说\"\n            placement=\"bottom\"\n        >\n            <Button\n                style={{padding: 0, gap: 3, ...style}}\n                onClick={handleClick}\n                icon={\n                    isSubscribed\n                        ? <IconSubscribeFilled style={{color: '#FFA400'}} />\n                        : <IconSubscribe />\n                }\n                type=\"text\"\n                size={size}\n            >\n                {showText\n                    ? '订阅' + (showCount ? ` ${subscribedNumber}` : '')\n                    : undefined\n                }\n            </Button>\n        </Popconfirm>\n    );\n};\n\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPSubscribeButton/index.tsx b/src/components/MCP/MCPSubscribeButton/index.tsx
--- a/src/components/MCP/MCPSubscribeButton/index.tsx	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/components/MCP/MCPSubscribeButton/index.tsx	(date 1755094697754)
@@ -3,10 +3,10 @@
 import {Popconfirm} from 'antd';
 import {useBoolean} from 'huse';
 import {loadAppListForMCPServer, setMCPSubscribe, useAppListForMCPServer} from '@/regions/mcp/mcpServer';
-import {IconSubscribe} from '@/icons/mcp';
+import {IconSubscribeBlue} from '@/icons/mcp';
 import {openMCPApplicationModal} from '@/regions/mcp/mcpApplication';
 import {ApplicationBase} from '@/types/mcp/mcp';
-import {IconSubscribeFilled} from '../../../icons/mcp/index';
+import {IconSubscribeFilled} from '@/icons/mcp';
 import {useMCPWorkspaceId} from '../hooks';
 
 interface Props {
@@ -18,9 +18,20 @@
     refresh?: () => void;
     style?: CSSProperties;
     size?: ButtonProps['size'];
+    iconColor?: string;
+    className?: string;
 }
 
-export const MCPSubscribeButton = ({id, workspaceId, style, showText, showCount, size, enableGlobalParams}: Props) => {
+export const MCPSubscribeButton = ({
+    id,
+    workspaceId,
+    style,
+    showText,
+    showCount,
+    size,
+    enableGlobalParams,
+    className,
+}: Props) => {
     const [open, {on, off}] = useBoolean(false);
     const spaceId = useMCPWorkspaceId();
     const appList = useAppListForMCPServer(id);
@@ -107,10 +118,11 @@
             <Button
                 style={{padding: 0, gap: 3, ...style}}
                 onClick={handleClick}
+                className={className}
                 icon={
                     isSubscribed
                         ? <IconSubscribeFilled style={{color: '#FFA400'}} />
-                        : <IconSubscribe />
+                        : <IconSubscribeBlue />
                 }
                 type="text"
                 size={size}
Index: src/components/MCP/PublishInfo.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, Typography} from 'antd';\nimport styled from '@emotion/styled';\nimport {css, cx} from '@emotion/css';\nimport {CSSProperties, useMemo} from 'react';\nimport UserAvatar from '@/design/UserAvatar';\nimport {overflowHiddenCss} from '@/styles/components';\nimport {howLongAgo} from '@/utils/date';\n\nconst Text = styled(Typography.Text)`\n    font-size: 12px;\n    line-height: 20px;\n`;\n\nconst flexZero = css`\n    flex-shrink: 0;\n    flex-grow: 0;\n`;\ninterface Props {\n    className?: string;\n    color?: string;\n    username?: string;\n    time?: string | number | Date;\n    style?: CSSProperties;\n}\n\nexport default function PublishInfo({className, username, time, color = '#545454', style}: Props) {\n    const timeStr = useMemo(\n        () => {\n            try {\n                return howLongAgo(time);\n            } catch (e) {\n                return '0秒';\n            }\n        },\n        [time]\n    );\n    return (\n        <Flex className={cx(overflowHiddenCss, className)} style={style} align=\"center\" gap={4}>\n            <UserAvatar username={username} iconSize={18} className={flexZero} />\n            <Text ellipsis style={{color}}>\n                {username} 发布于 {timeStr}前\n            </Text>\n        </Flex>\n    );\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/PublishInfo.tsx b/src/components/MCP/PublishInfo.tsx
--- a/src/components/MCP/PublishInfo.tsx	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/components/MCP/PublishInfo.tsx	(date 1755094697754)
@@ -1,8 +1,7 @@
 import {Flex, Typography} from 'antd';
 import styled from '@emotion/styled';
-import {css, cx} from '@emotion/css';
+import {cx} from '@emotion/css';
 import {CSSProperties, useMemo} from 'react';
-import UserAvatar from '@/design/UserAvatar';
 import {overflowHiddenCss} from '@/styles/components';
 import {howLongAgo} from '@/utils/date';
 
@@ -11,10 +10,6 @@
     line-height: 20px;
 `;
 
-const flexZero = css`
-    flex-shrink: 0;
-    flex-grow: 0;
-`;
 interface Props {
     className?: string;
     color?: string;
@@ -36,7 +31,6 @@
     );
     return (
         <Flex className={cx(overflowHiddenCss, className)} style={style} align="center" gap={4}>
-            <UserAvatar username={username} iconSize={18} className={flexZero} />
             <Text ellipsis style={{color}}>
                 {username} 发布于 {timeStr}前
             </Text>
Index: src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, Space, Divider, Typography} from 'antd';\nimport styled from '@emotion/styled';\nimport {useCallback} from 'react';\nimport {useNavigate} from 'react-router-dom';\nimport {css} from '@emotion/css';\nimport {MCPDetailLink} from '@/links/mcp';\nimport {MCPCollectButton} from '@/components/MCP/MCPCollectButton';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport {apiPostViewCount} from '@/api/mcp';\nimport {IconEye} from '@/icons/mcp';\nimport MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';\nimport TagGroup from '@/components/MCP/TagGroup';\nimport MCPCard from '@/design/MCP/MCPCard';\nimport MCPServerAvatar from '@/components/MCP/MCPServerAvatar';\nimport {overflowHiddenCss} from '@/styles/components';\nimport PublishInfo from '@/components/MCP/PublishInfo';\nimport ServerTypeTag from './ServerTypeTag';\n\nconst containerCss = css`\n    padding: 16px 20px 12px;\n`;\n\nconst Gray = styled.span`\n    font-size: 12px;\n    line-height: 20px;\n`;\n\nconst TooltipContainer = styled.div`\n    max-height: 200px;\n    word-wrap: break-word;\n    overflow: auto;\n`;\n\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n}\nconst SquireMCPCard = ({server, refresh}: Props) => {\n    const {\n        id,\n        workspaceId,\n        name,\n        departmentName,\n        serverSourceType,\n        serverProtocolType,\n        description,\n        labels,\n        icon,\n        viewCount,\n        favorite,\n        publishTime,\n        publishUser,\n    } = server;\n    const navigate = useNavigate();\n\n    const handleClick = useCallback(\n        async () => {\n            await apiPostViewCount({mcpServerId: id});\n            navigate(MCPDetailLink.toUrl({mcpId: id}));\n        },\n        [navigate, id]\n    );\n\n    return (\n        <MCPCard vertical onClick={handleClick} className={containerCss}>\n            <ServerTypeTag style={{position: 'absolute', right: 0, top: 0}} type={serverSourceType} />\n            <Flex gap={14} align=\"center\">\n                <MCPServerAvatar icon={icon} />\n                <Flex vertical justify=\"space-between\" style={{overflow: 'hidden'}} gap={4}>\n                    <Typography.Title level={4} ellipsis>{name}</Typography.Title>\n                    <Typography.Text\n                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}\n                    >\n                        {departmentName || '暂无部门信息'}\n                    </Typography.Text>\n                </Flex>\n            </Flex>\n            <Typography.Paragraph\n                type=\"secondary\"\n                ellipsis={{rows: 2, tooltip: <TooltipContainer>{description}</TooltipContainer>}}\n                style={{margin: '16px 0 12px', height: 44}}\n            >\n                {description || '暂无描述'}\n            </Typography.Paragraph>\n            <Flex align=\"center\" style={{overflow: 'hidden'}} gap={4}>\n                <MCPServerProtocolTypeTag type={serverProtocolType} />\n                <TagGroup\n                    labels={labels.map(label => ({id: label.id, label: label.labelValue}))}\n                    prefix={null}\n                    style={{flexShrink: 1, overflow: 'hidden'}}\n                    color=\"light-purple\"\n                    gap={4}\n                />\n            </Flex>\n            <Divider style={{margin: '16px 0 8px'}} />\n            <Flex justify=\"space-between\" align=\"center\" className={overflowHiddenCss}>\n                <Flex align=\"center\" gap={6} className={overflowHiddenCss}>\n                    <Flex align=\"center\" gap={4}>\n                        <IconEye />{viewCount}\n                    </Flex>\n                    <PublishInfo username={publishUser} time={publishTime} />\n                </Flex>\n                <Gray />\n                <Space size={4}>\n                    <MCPCollectButton refresh={refresh} favorite={favorite} serverId={id} size=\"small\" />\n                    <MCPSubscribeButton\n                        refresh={refresh}\n                        showText\n                        workspaceId={workspaceId}\n                        id={id}\n                        size=\"small\"\n                    />\n                </Space>\n            </Flex>\n        </MCPCard>\n    );\n};\n\nexport default SquireMCPCard;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx b/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx
--- a/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx	(date 1755094697750)
@@ -1,119 +1,50 @@
-import {Flex, Space, Divider, Typography} from 'antd';
-import styled from '@emotion/styled';
-import {useCallback} from 'react';
+import {useCallback, MouseEvent} from 'react';
 import {useNavigate} from 'react-router-dom';
-import {css} from '@emotion/css';
-import {MCPDetailLink} from '@/links/mcp';
-import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
-import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
+import {MCPDetailLink, MCPPlaygroundLink} from '@/links/mcp';
 import {MCPServerBase} from '@/types/mcp/mcp';
 import {apiPostViewCount} from '@/api/mcp';
-import {IconEye} from '@/icons/mcp';
-import MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';
-import TagGroup from '@/components/MCP/TagGroup';
-import MCPCard from '@/design/MCP/MCPCard';
-import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
-import {overflowHiddenCss} from '@/styles/components';
-import PublishInfo from '@/components/MCP/PublishInfo';
-import ServerTypeTag from './ServerTypeTag';
-
-const containerCss = css`
-    padding: 16px 20px 12px;
-`;
-
-const Gray = styled.span`
-    font-size: 12px;
-    line-height: 20px;
-`;
-
-const TooltipContainer = styled.div`
-    max-height: 200px;
-    word-wrap: break-word;
-    overflow: auto;
-`;
+import BaseMCPCard from '@/components/MCP/BaseMCPCard';
 
 interface Props {
     server: MCPServerBase;
     refresh: () => void;
 }
 const SquireMCPCard = ({server, refresh}: Props) => {
-    const {
-        id,
-        workspaceId,
-        name,
-        departmentName,
-        serverSourceType,
-        serverProtocolType,
-        description,
-        labels,
-        icon,
-        viewCount,
-        favorite,
-        publishTime,
-        publishUser,
-    } = server;
     const navigate = useNavigate();
 
     const handleClick = useCallback(
         async () => {
-            await apiPostViewCount({mcpServerId: id});
-            navigate(MCPDetailLink.toUrl({mcpId: id}));
+            await apiPostViewCount({mcpServerId: server.id});
+            navigate(MCPDetailLink.toUrl({mcpId: server.id}));
+        },
+        [navigate, server.id]
+    );
+
+    const handleViewCountClick = useCallback(
+        (e: MouseEvent) => {
+            e.stopPropagation();
+            navigate(MCPDetailLink.toUrl({mcpId: server.id, tab: 'overview'}));
         },
-        [navigate, id]
+        [navigate, server.id]
+    );
+
+    const handlePlaygroundClick = useCallback(
+        (e: MouseEvent) => {
+            e.stopPropagation();
+            window.open(MCPPlaygroundLink.toUrl({serverId: server.id}), '_blank');
+        },
+        [server.id]
     );
 
     return (
-        <MCPCard vertical onClick={handleClick} className={containerCss}>
-            <ServerTypeTag style={{position: 'absolute', right: 0, top: 0}} type={serverSourceType} />
-            <Flex gap={14} align="center">
-                <MCPServerAvatar icon={icon} />
-                <Flex vertical justify="space-between" style={{overflow: 'hidden'}} gap={4}>
-                    <Typography.Title level={4} ellipsis>{name}</Typography.Title>
-                    <Typography.Text
-                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}
-                    >
-                        {departmentName || '暂无部门信息'}
-                    </Typography.Text>
-                </Flex>
-            </Flex>
-            <Typography.Paragraph
-                type="secondary"
-                ellipsis={{rows: 2, tooltip: <TooltipContainer>{description}</TooltipContainer>}}
-                style={{margin: '16px 0 12px', height: 44}}
-            >
-                {description || '暂无描述'}
-            </Typography.Paragraph>
-            <Flex align="center" style={{overflow: 'hidden'}} gap={4}>
-                <MCPServerProtocolTypeTag type={serverProtocolType} />
-                <TagGroup
-                    labels={labels.map(label => ({id: label.id, label: label.labelValue}))}
-                    prefix={null}
-                    style={{flexShrink: 1, overflow: 'hidden'}}
-                    color="light-purple"
-                    gap={4}
-                />
-            </Flex>
-            <Divider style={{margin: '16px 0 8px'}} />
-            <Flex justify="space-between" align="center" className={overflowHiddenCss}>
-                <Flex align="center" gap={6} className={overflowHiddenCss}>
-                    <Flex align="center" gap={4}>
-                        <IconEye />{viewCount}
-                    </Flex>
-                    <PublishInfo username={publishUser} time={publishTime} />
-                </Flex>
-                <Gray />
-                <Space size={4}>
-                    <MCPCollectButton refresh={refresh} favorite={favorite} serverId={id} size="small" />
-                    <MCPSubscribeButton
-                        refresh={refresh}
-                        showText
-                        workspaceId={workspaceId}
-                        id={id}
-                        size="small"
-                    />
-                </Space>
-            </Flex>
-        </MCPCard>
+        <BaseMCPCard
+            server={server}
+            refresh={refresh}
+            showDepartment
+            onCardClick={handleClick}
+            onViewCountClick={handleViewCountClick}
+            onPlaygroundClick={handlePlaygroundClick}
+        />
     );
 };
 
Index: src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, Divider, Typography} from 'antd';\nimport {memo, useCallback, useMemo} from 'react';\nimport {useNavigate} from 'react-router-dom';\nimport {css} from '@emotion/css';\nimport styled from '@emotion/styled';\nimport {MCPEditLink} from '@/links/mcp';\nimport {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport {useMCPWorkspaceId} from '@/components/MCP/hooks';\nimport {MCPDetailButton} from '@/components/MCP/MCPDetailButton';\nimport MCPServerAvatar from '@/components/MCP/MCPServerAvatar';\nimport MCPCard from '@/design/MCP/MCPCard';\nimport MCPServerTypeTag from '@/components/MCP/MCPServerTypeTag';\nimport MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';\nimport TagGroup from '@/components/MCP/TagGroup';\nimport UpdateInfo from '@/components/MCP/UpdateInfo';\n\nconst containerCss = css`\n    padding: 0 20px 12px;\n`;\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n}\n\nconst TooltipContainer = styled.div`\n    max-height: 200px;\n    word-wrap: break-word;\n    overflow: auto;\n`;\n\nconst SpaceMCPCard = ({server, refresh}: Props) => {\n    const spaceId = useMCPWorkspaceId();\n    const navigate = useNavigate();\n\n    const handleClick = useCallback(\n        () => {\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const tags = useMemo(\n        () => (server.labels ?? []).map(label => label.labelValue),\n        [server.labels]\n    );\n\n    return (\n        <MCPCard vertical onClick={handleClick} className={containerCss}>\n            <Flex align=\"flex-end\" gap={14} style={{overflow: 'hidden', marginRight: '-20px'}}>\n                <MCPServerAvatar icon={server.icon} style={{marginBottom: 4}} />\n                <Flex vertical style={{overflow: 'hidden', flex: 1}}>\n                    <Flex align=\"flex-start\" justify=\"space-between\" gap={8}>\n                        <Typography.Title level={4} style={{margin: '16px 0 4px'}} ellipsis>\n                            {server.name}\n                        </Typography.Title>\n                        <MCPReleaseStatus\n                            status={server.serverStatus}\n                            publishType={server.serverPublishType}\n                            style={{flexShrink: 0}}\n                        />\n                    </Flex>\n                    <Typography.Text\n                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}\n                    >\n                        {server.departmentName || '暂无部门信息'}\n                    </Typography.Text>\n                </Flex>\n            </Flex>\n            <Typography.Paragraph\n                type=\"secondary\"\n                ellipsis={{tooltip: <TooltipContainer>{server.description}</TooltipContainer>, rows: 2}}\n                style={{margin: '16px 0 12px', height: 44}}\n            >\n                {server.description || '暂无描述'}\n            </Typography.Paragraph>\n            <Flex gap={4}>\n                <MCPServerTypeTag type={server.serverSourceType} />\n                <MCPServerProtocolTypeTag type={server.serverProtocolType} />\n                <TagGroup\n                    labels={tags.map((label, index) => ({id: index, label}))}\n                    color=\"light-purple\"\n                    prefix={null}\n                    style={{flexShrink: 1, overflow: 'hidden'}}\n                    gap={4}\n                />\n            </Flex>\n            <Divider style={{margin: '16px 0 8px', borderColor: 'rgba(75, 108, 159, 0.15)'}} />\n            <Flex justify=\"space-between\" align=\"center\" gap={4}>\n                <UpdateInfo username={server.lastModifyUser} time={server.lastModifyTime} variant=\"space-card\" />\n                <Flex align=\"center\" gap={8}>\n                    <MCPDetailButton serverId={server.id} size=\"small\" />\n                    <Divider\n                        type=\"vertical\"\n                        style={{height: 16, margin: 0}}\n                    />\n                    <MCPSubscribeButton\n                        refresh={refresh}\n                        id={server.id}\n                        // 这个 Server 是空间下的，后端没有返回 Server 的 workspaceId\n                        // 所以这里用的是 页面级别的 workspaceId\n                        workspaceId={spaceId}\n                        showText\n                        showCount\n                        size=\"small\"\n                    />\n                </Flex>\n            </Flex>\n        </MCPCard>\n    );\n};\n\nexport default memo(SpaceMCPCard);\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
--- a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(date 1755094697748)
@@ -1,35 +1,19 @@
-import {Flex, Divider, Typography} from 'antd';
-import {memo, useCallback, useMemo} from 'react';
+import {memo, MouseEvent, useCallback} from 'react';
 import {useNavigate} from 'react-router-dom';
-import {css} from '@emotion/css';
-import styled from '@emotion/styled';
-import {MCPEditLink} from '@/links/mcp';
-import {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';
+import {Button} from '@panda-design/components';
+import cx from 'classnames';
+import {MCPEditLink, MCPSpaceDetailLink} from '@/links/mcp';
 import {MCPServerBase} from '@/types/mcp/mcp';
-import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
 import {useMCPWorkspaceId} from '@/components/MCP/hooks';
-import {MCPDetailButton} from '@/components/MCP/MCPDetailButton';
-import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
-import MCPCard from '@/design/MCP/MCPCard';
-import MCPServerTypeTag from '@/components/MCP/MCPServerTypeTag';
-import MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';
-import TagGroup from '@/components/MCP/TagGroup';
-import UpdateInfo from '@/components/MCP/UpdateInfo';
+import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
+import BaseMCPCard from '@/components/MCP/BaseMCPCard';
+import {actionButtonHoverStyle} from '@/components/MCP/BaseMCPCard/BaseMCPCard.styles';
 
-const containerCss = css`
-    padding: 0 20px 12px;
-`;
 interface Props {
     server: MCPServerBase;
     refresh: () => void;
 }
 
-const TooltipContainer = styled.div`
-    max-height: 200px;
-    word-wrap: break-word;
-    overflow: auto;
-`;
-
 const SpaceMCPCard = ({server, refresh}: Props) => {
     const spaceId = useMCPWorkspaceId();
     const navigate = useNavigate();
@@ -41,73 +25,63 @@
         [navigate, spaceId, server.id]
     );
 
-    const tags = useMemo(
-        () => (server.labels ?? []).map(label => label.labelValue),
-        [server.labels]
+    const handleViewCountClick = useCallback(
+        (e: MouseEvent) => {
+            e.stopPropagation();
+            navigate(MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview'}));
+        },
+        [navigate, server.id]
+    );
+
+    const handleBasicInfoClick = useCallback(
+        (e: MouseEvent) => {
+            e.stopPropagation();
+            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'basicInfo'}));
+        },
+        [navigate, spaceId, server.id]
     );
 
-    return (
-        <MCPCard vertical onClick={handleClick} className={containerCss}>
-            <Flex align="flex-end" gap={14} style={{overflow: 'hidden', marginRight: '-20px'}}>
-                <MCPServerAvatar icon={server.icon} style={{marginBottom: 4}} />
-                <Flex vertical style={{overflow: 'hidden', flex: 1}}>
-                    <Flex align="flex-start" justify="space-between" gap={8}>
-                        <Typography.Title level={4} style={{margin: '16px 0 4px'}} ellipsis>
-                            {server.name}
-                        </Typography.Title>
-                        <MCPReleaseStatus
-                            status={server.serverStatus}
-                            publishType={server.serverPublishType}
-                            style={{flexShrink: 0}}
-                        />
-                    </Flex>
-                    <Typography.Text
-                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}
-                    >
-                        {server.departmentName || '暂无部门信息'}
-                    </Typography.Text>
-                </Flex>
-            </Flex>
-            <Typography.Paragraph
-                type="secondary"
-                ellipsis={{tooltip: <TooltipContainer>{server.description}</TooltipContainer>, rows: 2}}
-                style={{margin: '16px 0 12px', height: 44}}
-            >
-                {server.description || '暂无描述'}
-            </Typography.Paragraph>
-            <Flex gap={4}>
-                <MCPServerTypeTag type={server.serverSourceType} />
-                <MCPServerProtocolTypeTag type={server.serverProtocolType} />
-                <TagGroup
-                    labels={tags.map((label, index) => ({id: index, label}))}
-                    color="light-purple"
-                    prefix={null}
-                    style={{flexShrink: 1, overflow: 'hidden'}}
-                    gap={4}
-                />
-            </Flex>
-            <Divider style={{margin: '16px 0 8px', borderColor: 'rgba(75, 108, 159, 0.15)'}} />
-            <Flex justify="space-between" align="center" gap={4}>
-                <UpdateInfo username={server.lastModifyUser} time={server.lastModifyTime} variant="space-card" />
-                <Flex align="center" gap={8}>
-                    <MCPDetailButton serverId={server.id} size="small" />
-                    <Divider
-                        type="vertical"
-                        style={{height: 16, margin: 0}}
-                    />
-                    <MCPSubscribeButton
-                        refresh={refresh}
-                        id={server.id}
-                        // 这个 Server 是空间下的，后端没有返回 Server 的 workspaceId
-                        // 所以这里用的是 页面级别的 workspaceId
-                        workspaceId={spaceId}
-                        showText
-                        showCount
-                        size="small"
-                    />
-                </Flex>
-            </Flex>
-        </MCPCard>
+    const handleToolsConfigClick = useCallback(
+        (e: MouseEvent) => {
+            e.stopPropagation();
+            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));
+        },
+        [navigate, spaceId, server.id]
+    );
+
+    const renderActions = useCallback(
+        () => (
+            <>
+
+                <MCPSubscribeButton
+                    refresh={refresh}
+                    workspaceId={spaceId || server.workspaceId}
+                    id={server.id}
+                    className={cx(actionButtonHoverStyle)}
+                    showText={false}
+                    iconColor="#0083FF"
+                />
+                <Button type="text" onClick={handleBasicInfoClick} className={cx(actionButtonHoverStyle)}>
+                    基本信息
+                </Button>
+                <Button type="text" onClick={handleToolsConfigClick} className={cx(actionButtonHoverStyle)}>
+                    工具配置
+                </Button>
+            </>
+        ),
+        [handleBasicInfoClick, handleToolsConfigClick, refresh, spaceId, server.workspaceId, server.id]
+    );
+
+    return (
+        <BaseMCPCard
+            server={server}
+            refresh={refresh}
+            showDepartment={false}
+            workspaceId={spaceId}
+            onCardClick={handleClick}
+            onViewCountClick={handleViewCountClick}
+            renderActions={renderActions}
+        />
     );
 };
 
Index: src/components/MCP/MCPCollectButton/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {StarFilled, StarOutlined} from '@ant-design/icons';\nimport {Button, ButtonProps, message} from '@panda-design/components';\nimport {MouseEvent} from 'react';\nimport {apiDeleteServerFavorite, apiPutServerFavorite} from '@/api/mcp';\n\ninterface Props {\n    serverId: number;\n    favorite: boolean;\n    refresh: () => void;\n    size?: ButtonProps['size'];\n}\n\nexport const MCPCollectButton = ({serverId, refresh, favorite, size}: Props) => {\n    const handleClick = async (e: MouseEvent) => {\n        e.stopPropagation();\n        e.preventDefault();\n        if (favorite) {\n            await apiDeleteServerFavorite({mcpServerId: serverId});\n        } else {\n            await apiPutServerFavorite({mcpServerId: serverId});\n        }\n        message.success(favorite ? '取消收藏成功' : '收藏成功');\n        refresh();\n    };\n    return (\n        <Button\n            onClick={handleClick}\n            type=\"text\"\n            size={size}\n            icon={\n                favorite\n                    ? <StarFilled style={{color: '#FFA400'}} />\n                    : <StarOutlined />\n            }\n        >\n            收藏\n        </Button>\n    );\n};\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPCollectButton/index.tsx b/src/components/MCP/MCPCollectButton/index.tsx
--- a/src/components/MCP/MCPCollectButton/index.tsx	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/components/MCP/MCPCollectButton/index.tsx	(date 1755094697753)
@@ -1,6 +1,6 @@
 import {StarFilled, StarOutlined} from '@ant-design/icons';
 import {Button, ButtonProps, message} from '@panda-design/components';
-import {MouseEvent} from 'react';
+import {MouseEvent, CSSProperties} from 'react';
 import {apiDeleteServerFavorite, apiPutServerFavorite} from '@/api/mcp';
 
 interface Props {
@@ -8,9 +8,22 @@
     favorite: boolean;
     refresh: () => void;
     size?: ButtonProps['size'];
+    style?: CSSProperties;
+    showText?: boolean;
+    iconColor?: string;
+    className?: string;
 }
 
-export const MCPCollectButton = ({serverId, refresh, favorite, size}: Props) => {
+export const MCPCollectButton = ({
+    serverId,
+    refresh,
+    favorite,
+    size,
+    style,
+    showText = true,
+    iconColor,
+    className,
+}: Props) => {
     const handleClick = async (e: MouseEvent) => {
         e.stopPropagation();
         e.preventDefault();
@@ -27,13 +40,15 @@
             onClick={handleClick}
             type="text"
             size={size}
+            style={style}
+            className={className}
             icon={
                 favorite
                     ? <StarFilled style={{color: '#FFA400'}} />
-                    : <StarOutlined />
+                    : <StarOutlined style={{color: iconColor}} />
             }
         >
-            收藏
+            {showText ? '收藏' : undefined}
         </Button>
     );
 };
Index: src/design/Dev/localStorageGetterSetter.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>/* eslint-disable no-console */\nexport const envMap = {\n    sandbox: '沙盒环境',\n    'icode-sandbox': '沙盒环境-icode',\n    'mcp-sandbox': '沙盒环境-MCP',\n    ernieDev: '一言dev',\n    canary: '金丝雀环境',\n    weizhen02: '魏臻',\n    huyufei: '宇飞',\n    marongzhen: '荣臻',\n    shenlin09: '申琳',\n    liuzhao03: '刘召',\n    yangyichen01: '翌晨',\n    xuyuan09: '许愿',\n    zhangenming02: '恩铭',\n    zhangcong06: '张聪',\n    wangkaiyuan02: '开源',\n    kangyuxin: '育鑫',\n    liqiyi: '齐一',\n    yandong02: '闫冬',\n    dongtaomin: '陶民',\n    dingzhe: '丁哲',\n};\n\nexport const getEnvValueFromLocalStorage = (): string | undefined => {\n    try {\n        const productConfigsValue = localStorage.getItem('@ls-storage-should-clear-lab-products');\n        const productConfigs = JSON.parse(productConfigsValue);\n        const {hostname} = new URL(productConfigs?.comatestack?.entry);\n        for (const env of Object.keys(envMap)) {\n\n            if (hostname.startsWith(`comatestack-${env}`)) {\n                return env;\n            }\n        }\n    }\n    catch {\n        // do nothing\n    }\n};\n\nexport const setLocalStorageEnvValue = (value: string): void => {\n    if (value === 'sandbox') {\n        localStorage.removeItem('@ls-storage-should-clear-lab-products');\n        window.location.reload();\n        return;\n    }\n    const productConfigs = {\n        comatestack: {\n            entry: `https://comatestack-${value}.now.baidu-int.com/index-comatestack.html`,\n        },\n        iplayground: {\n            entry: `https://comatestack-${value}.now.baidu-int.com/index-iplayground.html`,\n        },\n        ievalue: {\n            entry: `https://comatestack-${value}.now.baidu-int.com/index-ievalue.html`,\n        },\n        icode: {\n            entry: `https://comatestack-${value}.now.baidu-int.com/index-icode.html`,\n        },\n    };\n    const productConfigsValue = JSON.stringify(productConfigs);\n    localStorage.setItem('@ls-storage-should-clear-lab-products', productConfigsValue);\n};\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/design/Dev/localStorageGetterSetter.tsx b/src/design/Dev/localStorageGetterSetter.tsx
--- a/src/design/Dev/localStorageGetterSetter.tsx	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/design/Dev/localStorageGetterSetter.tsx	(date 1755094697757)
@@ -20,6 +20,7 @@
     yandong02: '闫冬',
     dongtaomin: '陶民',
     dingzhe: '丁哲',
+    liancong: '连聪',
 };
 
 export const getEnvValueFromLocalStorage = (): string | undefined => {
Index: src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, List} from 'antd';\nimport {useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport styled from '@emotion/styled';\nimport {apiGetSquareServerList} from '@/api/mcp';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';\nimport MCPEmpty from '@/design/MCP/MCPEmpty';\nimport {FilterValues, TabValues} from '../SquireFilter';\nimport {useLoadMore} from '../../MCPSpace/MCPListPanel/hooks';\nimport {ALL_LABELS, OTHER_LABELS} from '../SquireFilter/LabelsFilterContent';\nimport SquireMCPCard from './SquireMCPCard';\n\nconst Container = styled.div`\n    flex: 1;\n    overflow-y: auto;\n    ::-webkit-scrollbar {\n        display: none;\n    }\n`;\n\nconst PAGE_SIZE = 12;\n\nconst CARD_HEIGHT = 235;\n\nconst processEmptyText = (filterData?: FilterValues & TabValues) => {\n    if (filterData?.keywords?.trim()\n        || !(filterData?.labels?.length === 1 && filterData?.labels[0] === ALL_LABELS)\n        || filterData?.serverSourceType\n        || filterData?.serverProtocolType\n    ) {\n        return '暂无结果';\n    }\n    if (filterData?.favorite) {\n        return '暂无收藏的MCP Server';\n    }\n    if (filterData?.isMine) {\n        return '暂无发布的MCP Server';\n    }\n    return '暂无MCP Server';\n};\n\nconst formatSearchParams = (searchParams?: FilterValues & TabValues) => {\n    return {\n        viewOrder: searchParams.viewOrder,\n        publishOrder: searchParams.publishOrder,\n        ...(searchParams.tab !== 'all' ? {[searchParams.tab]: true} : {}),\n        labels: searchParams.labels?.includes(ALL_LABELS)\n            ? undefined\n            : searchParams.labels?.includes(OTHER_LABELS)\n                ? '-1'\n                : searchParams.labels?.map(label => label).join(','),\n        keywords: searchParams?.keywords?.trim() || undefined,\n        serverSourceType: searchParams?.serverSourceType === 'all' ? undefined : searchParams.serverSourceType,\n        serverProtocolType: searchParams.serverProtocolType === 'all' ? undefined : searchParams.serverProtocolType,\n    };\n};\n\ninterface Props {\n    searchParams?: FilterValues & TabValues; // Define the type according to your needs\n}\nconst SquirePanel = ({searchParams}: Props) => {\n    const [pageSize, setPageSize] = useState(PAGE_SIZE);\n    const api = useCallback(\n        (params: {current: number, limit: number}) => {\n            return apiGetSquareServerList({\n                ...formatSearchParams(searchParams),\n                platformType: 'hub',\n                size: params.limit,\n                pn: params.current,\n            });\n        },\n        [searchParams]\n    );\n    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, pageSize);\n\n    useEffect(\n        () => {\n            refresh();\n        },\n        [refresh]\n    );\n\n    const emptyText = useMemo(\n        () => processEmptyText(searchParams),\n        [searchParams]\n    );\n\n    useLayoutEffect(\n        () => {\n            const container = document.getElementById('scrollableDiv');\n            const height = container?.clientHeight || 0;\n            let rows = Math.ceil(height / CARD_HEIGHT);\n            // 因为每行可以有3个或2个卡片，所以需要确保pagesizepageSize是2和3的公倍数，所以需要调整rows的值为偶数。\n            rows = rows % 2 === 0 ? rows : rows + 1;\n            const maxItems = rows * 3;\n            if (maxItems > PAGE_SIZE) {\n                setPageSize(maxItems);\n            }\n        },\n        []\n    );\n\n    return (\n        <Container id=\"scrollableDiv\">\n            <InfiniteScroll\n                style={{overflow: 'none'}}\n                dataLength={list.length || 0}\n                next={loadMore}\n                hasMore={total > list.length}\n                loader={<Flex justify=\"center\" align=\"center\"><div>加载中...</div></Flex>}\n                scrollableTarget=\"scrollableDiv\"\n            >\n                {(\n                    <List\n                        grid={{\n                            gutter: 12,\n                            column: 2,\n                            xs: 2,\n                            sm: 3,\n                            md: 3,\n                            lg: 3,\n                            xl: 3,\n                            xxl: 3,\n                        }}\n                        dataSource={list}\n                        rowKey=\"id\"\n                        renderItem={server => (\n                            <List.Item>\n                                <SquireMCPCard refresh={refresh} key={server.id} server={server} />\n                            </List.Item>\n                        )}\n                        locale={{\n                            emptyText: (\n                                <MCPEmpty\n                                    description={(\n                                        <Flex justify=\"center\">\n                                            {emptyText}\n                                        </Flex>\n                                    )}\n                                />\n                            ),\n                        }}\n                    />\n                )}\n            </InfiniteScroll>\n            <CreateMCPAppModal />\n        </Container>\n    );\n};\n\nexport default SquirePanel;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx b/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx
--- a/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx	(date 1755094697751)
@@ -114,7 +114,7 @@
                 {(
                     <List
                         grid={{
-                            gutter: 12,
+                            gutter: 20,
                             column: 2,
                             xs: 2,
                             sm: 3,
Index: src/components/MCP/TagGroup.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {Flex, Tooltip} from 'antd';\nimport {Tag, TagColor} from '@panda-design/components';\nimport {CSSProperties, ReactNode} from 'react';\nimport {IconTag} from '@/icons/mcp';\n\ninterface Label {\n    id: number;\n    label: string;\n}\nconst TooltipTitleWrapper = styled.div`\n    display: flex;\n    flex-wrap: wrap;\n    gap: 8px;\n    padding: 4px 0;\n`;\n\nconst COLOR_MAP: Record<string, CSSProperties> = {\n    'light-purple': {\n        backgroundColor: '#EBE6F9',\n        color: '#8264DE',\n    },\n    'gray': {\n        backgroundColor: '#E2E8F0',\n        color: '#4B6C9F',\n    },\n};\n\ninterface Props {\n    prefix?: ReactNode;\n    color?: TagColor;\n    labels: Label[];\n    maxNum?: number;\n    style?: CSSProperties;\n    gap?: number;\n}\n\nconst TagGroup = ({prefix = <IconTag />, labels, maxNum = 2, color = 'info', style, gap = 8}: Props) => {\n    const colorStyle = COLOR_MAP[color];\n    const innerColor = COLOR_MAP[color] ? undefined : color;\n    return (\n        <Flex style={{zIndex: 1, gap, ...style}} align=\"center\">\n            {prefix}\n            {\n                labels.slice(0, maxNum).map(label => {\n                    return (\n                        <Tag\n                            type=\"flat\"\n                            color={innerColor}\n                            key={label.id}\n                            style={{\n                                margin: 0,\n                                flexShrink: 1,\n                                overflow: 'hidden',\n                                textOverflow: 'ellipsis',\n                                ...colorStyle,\n                            }}\n                        >\n                            {label.label}\n                        </Tag>\n                    );\n                })\n            }\n            {\n                labels.length > maxNum && (\n                    <Tooltip\n                        title={\n                            <TooltipTitleWrapper>\n                                {labels.slice(maxNum).map(label => {\n                                    return (\n                                        <Tag\n                                            type=\"flat\"\n                                            color={innerColor}\n                                            key={label.id}\n                                            style={{\n                                                margin: 0,\n                                                ...colorStyle,\n                                            }}\n                                        >\n                                            {label.label}\n                                        </Tag>\n                                    );\n                                })}\n                            </TooltipTitleWrapper>\n                        }\n                    >\n                        <Tag\n                            type=\"flat\"\n                            color={innerColor}\n                            style={{\n                                margin: 0,\n                                ...colorStyle,\n                            }}\n                        >\n                            +{labels.length - maxNum}\n                        </Tag>\n                    </Tooltip>\n                )\n            }\n        </Flex>\n    );\n};\n\nexport default TagGroup;\n\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/TagGroup.tsx b/src/components/MCP/TagGroup.tsx
--- a/src/components/MCP/TagGroup.tsx	(revision 590f2c484e63a06806e1f51af9d353729af61f45)
+++ b/src/components/MCP/TagGroup.tsx	(date 1755094697755)
@@ -39,10 +39,10 @@
     const colorStyle = COLOR_MAP[color];
     const innerColor = COLOR_MAP[color] ? undefined : color;
     return (
-        <Flex style={{zIndex: 1, gap, ...style}} align="center">
+        <Flex style={{zIndex: 1, gap, height: 22, ...style}} align="center">
             {prefix}
             {
-                labels.slice(0, maxNum).map(label => {
+                labels.length > 0 ? labels.slice(0, maxNum).map(label => {
                     return (
                         <Tag
                             type="flat"
@@ -59,7 +59,19 @@
                             {label.label}
                         </Tag>
                     );
-                })
+                }) : (
+                    <Tag
+                        type="flat"
+                        color={innerColor}
+                        style={{
+                            margin: 0,
+                            opacity: 0,
+                            ...colorStyle,
+                        }}
+                    >
+                        占位标签
+                    </Tag>
+                )
             }
             {
                 labels.length > maxNum && (
