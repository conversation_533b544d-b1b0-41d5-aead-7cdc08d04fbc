Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># See https://help.github.com/articles/ignoring-files/ for more about ignoring files.\n\n# dependencies\n/node_modules\n/.pnp\n.pnp.js\n\n# testing\n/coverage\n\n# production\n/build\n/output\n/.comate-f2c\n\n# misc\n.DS_Store\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/.gitignore	(date 1755004235086)
@@ -23,3 +23,12 @@
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
+.idea/.gitignore
+.idea/comate-stack-fe.iml
+.idea/modules.xml
+.idea/vcs.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+/.augment/rules/mode.md
+/.idea/jsLinters/eslint.xml
Index: src/icons/mcp/index.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import type { FC, SVGProps } from \"react\";\nimport { createIcon } from \"@panda-design/components\";\nimport SendActived from \"./SendActived\";\nimport AiTools1 from \"./AiTools1\";\nimport AiTools2 from \"./AiTools2\";\nimport AiTools3 from \"./AiTools3\";\nimport AiTools4 from \"./AiTools4\";\nimport Alert from \"./Alert\";\nimport ArrowRight from \"./ArrowRight\";\nimport Copy from \"./Copy\";\nimport Debug from \"./Debug\";\nimport Delete from \"./Delete\";\nimport Detail from \"./Detail\";\nimport Elipsis from \"./Elipsis\";\nimport ExitFullscreen from \"./ExitFullscreen\";\nimport Eye from \"./Eye\";\nimport Fullscreen from \"./Fullscreen\";\nimport Import from \"./Import\";\nimport LeftOutlined from \"./LeftOutlined\";\nimport LightMySpcae from \"./LightMySpcae\";\nimport LightPlayground from \"./LightPlayground\";\nimport List from \"./List\";\nimport Local from \"./Local\";\nimport LocalMcp from \"./LocalMcp\";\nimport McpAvatar from \"./McpAvatar\";\nimport MySpcae from \"./MySpcae\";\nimport OffcialExample from \"./OffcialExample\";\nimport Organization from \"./Organization\";\nimport Params from \"./Params\";\nimport Playground from \"./Playground\";\nimport PlaygroundConfig from \"./PlaygroundConfig\";\nimport Refresh from \"./Refresh\";\nimport Remote from \"./Remote\";\nimport RemoteMcp from \"./RemoteMcp\";\nimport Result from \"./Result\";\nimport RightArrow from \"./RightArrow\";\nimport Send from \"./Send\";\nimport Setting from \"./Setting\";\nimport ShowMore from \"./ShowMore\";\nimport SortAsc from \"./SortAsc\";\nimport SortDesc from \"./SortDesc\";\nimport Sse from \"./Sse\";\nimport Standard from \"./Standard\";\nimport Stdio from \"./Stdio\";\nimport StdioMcp from \"./StdioMcp\";\nimport Step01 from \"./Step01\";\nimport Step02 from \"./Step02\";\nimport Step03 from \"./Step03\";\nimport Step04 from \"./Step04\";\nimport StopGenerate from \"./StopGenerate\";\nimport Subscribe from \"./Subscribe\";\nimport SubscribeFilled from \"./SubscribeFilled\";\nimport Subtract from \"./Subtract\";\nimport Tag from \"./Tag\";\nimport Tool from \"./Tool\";\nimport Unfold from \"./Unfold\";\n\nexport const IconSendActived = createIcon(SendActived);\nexport const IconAiTools1 = createIcon(AiTools1);\nexport const IconAiTools2 = createIcon(AiTools2);\nexport const IconAiTools3 = createIcon(AiTools3);\nexport const IconAiTools4 = createIcon(AiTools4);\nexport const IconAlert = createIcon(Alert);\nexport const IconArrowRight = createIcon(ArrowRight);\nexport const IconCopy = createIcon(Copy);\nexport const IconDebug = createIcon(Debug);\nexport const IconDelete = createIcon(Delete);\nexport const IconDetail = createIcon(Detail);\nexport const IconElipsis = createIcon(Elipsis);\nexport const IconExitFullscreen = createIcon(ExitFullscreen);\nexport const IconEye = createIcon(Eye);\nexport const IconFullscreen = createIcon(Fullscreen);\nexport const IconImport = createIcon(Import);\nexport const IconLeftOutlined = createIcon(LeftOutlined);\nexport const IconLightMySpcae = createIcon(LightMySpcae);\nexport const IconLightPlayground = createIcon(LightPlayground);\nexport const IconList = createIcon(List);\nexport const IconLocal = createIcon(Local);\nexport const IconLocalMcp = createIcon(LocalMcp);\nexport const IconMcpAvatar = createIcon(McpAvatar);\nexport const IconMySpcae = createIcon(MySpcae);\nexport const IconOffcialExample = createIcon(OffcialExample);\nexport const IconOrganization = createIcon(Organization);\nexport const IconParams = createIcon(Params);\nexport const IconPlayground = createIcon(Playground);\nexport const IconPlaygroundConfig = createIcon(PlaygroundConfig);\nexport const IconRefresh = createIcon(Refresh);\nexport const IconRemote = createIcon(Remote);\nexport const IconRemoteMcp = createIcon(RemoteMcp);\nexport const IconResult = createIcon(Result);\nexport const IconRightArrow = createIcon(RightArrow);\nexport const IconSend = createIcon(Send);\nexport const IconSetting = createIcon(Setting);\nexport const IconShowMore = createIcon(ShowMore);\nexport const IconSortAsc = createIcon(SortAsc);\nexport const IconSortDesc = createIcon(SortDesc);\nexport const IconSse = createIcon(Sse);\nexport const IconStandard = createIcon(Standard);\nexport const IconStdio = createIcon(Stdio);\nexport const IconStdioMcp = createIcon(StdioMcp);\nexport const IconStep01 = createIcon(Step01);\nexport const IconStep02 = createIcon(Step02);\nexport const IconStep03 = createIcon(Step03);\nexport const IconStep04 = createIcon(Step04);\nexport const IconStopGenerate = createIcon(StopGenerate);\nexport const IconSubscribe = createIcon(Subscribe);\nexport const IconSubscribeFilled = createIcon(SubscribeFilled);\nexport const IconSubtract = createIcon(Subtract);\nexport const IconTag = createIcon(Tag);\nexport const IconTool = createIcon(Tool);\nexport const IconUnfold = createIcon(Unfold);\n\nexport const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {\n    SendActived: IconSendActived,\n    aiTools1: IconAiTools1,\n    aiTools2: IconAiTools2,\n    aiTools3: IconAiTools3,\n    aiTools4: IconAiTools4,\n    alert: IconAlert,\n    arrowRight: IconArrowRight,\n    copy: IconCopy,\n    debug: IconDebug,\n    delete: IconDelete,\n    detail: IconDetail,\n    elipsis: IconElipsis,\n    exitFullscreen: IconExitFullscreen,\n    eye: IconEye,\n    fullscreen: IconFullscreen,\n    import: IconImport,\n    leftOutlined: IconLeftOutlined,\n    lightMySpcae: IconLightMySpcae,\n    lightPlayground: IconLightPlayground,\n    list: IconList,\n    local: IconLocal,\n    localMCP: IconLocalMcp,\n    mcpAvatar: IconMcpAvatar,\n    mySpcae: IconMySpcae,\n    offcialExample: IconOffcialExample,\n    organization: IconOrganization,\n    params: IconParams,\n    playground: IconPlayground,\n    playgroundConfig: IconPlaygroundConfig,\n    refresh: IconRefresh,\n    remote: IconRemote,\n    remoteMCP: IconRemoteMcp,\n    result: IconResult,\n    rightArrow: IconRightArrow,\n    send: IconSend,\n    setting: IconSetting,\n    showMore: IconShowMore,\n    sortAsc: IconSortAsc,\n    sortDesc: IconSortDesc,\n    sse: IconSse,\n    standard: IconStandard,\n    stdio: IconStdio,\n    stdioMCP: IconStdioMcp,\n    step01: IconStep01,\n    step02: IconStep02,\n    step03: IconStep03,\n    step04: IconStep04,\n    stopGenerate: IconStopGenerate,\n    subscribe: IconSubscribe,\n    subscribeFilled: IconSubscribeFilled,\n    subtract: IconSubtract,\n    tag: IconTag,\n    tool: IconTool,\n    unfold: IconUnfold,\n};\n\nexport default iconsMap;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/index.ts b/src/icons/mcp/index.ts
--- a/src/icons/mcp/index.ts	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/icons/mcp/index.ts	(date 1755004500435)
@@ -7,6 +7,10 @@
 import AiTools4 from "./AiTools4";
 import Alert from "./Alert";
 import ArrowRight from "./ArrowRight";
+import ArrowRight1 from "./ArrowRight1";
+import Case from "./Case";
+import Code from "./Code";
+import Comment from "./Comment";
 import Copy from "./Copy";
 import Debug from "./Debug";
 import Delete from "./Delete";
@@ -25,6 +29,7 @@
 import McpAvatar from "./McpAvatar";
 import MySpcae from "./MySpcae";
 import OffcialExample from "./OffcialExample";
+import Ops from "./Ops";
 import Organization from "./Organization";
 import Params from "./Params";
 import Playground from "./Playground";
@@ -52,6 +57,7 @@
 import SubscribeFilled from "./SubscribeFilled";
 import Subtract from "./Subtract";
 import Tag from "./Tag";
+import Test from "./Test";
 import Tool from "./Tool";
 import Unfold from "./Unfold";
 
@@ -62,6 +68,10 @@
 export const IconAiTools4 = createIcon(AiTools4);
 export const IconAlert = createIcon(Alert);
 export const IconArrowRight = createIcon(ArrowRight);
+export const IconArrowRight1 = createIcon(ArrowRight1);
+export const IconCase = createIcon(Case);
+export const IconCode = createIcon(Code);
+export const IconComment = createIcon(Comment);
 export const IconCopy = createIcon(Copy);
 export const IconDebug = createIcon(Debug);
 export const IconDelete = createIcon(Delete);
@@ -80,6 +90,7 @@
 export const IconMcpAvatar = createIcon(McpAvatar);
 export const IconMySpcae = createIcon(MySpcae);
 export const IconOffcialExample = createIcon(OffcialExample);
+export const IconOps = createIcon(Ops);
 export const IconOrganization = createIcon(Organization);
 export const IconParams = createIcon(Params);
 export const IconPlayground = createIcon(Playground);
@@ -107,6 +118,7 @@
 export const IconSubscribeFilled = createIcon(SubscribeFilled);
 export const IconSubtract = createIcon(Subtract);
 export const IconTag = createIcon(Tag);
+export const IconTest = createIcon(Test);
 export const IconTool = createIcon(Tool);
 export const IconUnfold = createIcon(Unfold);
 
@@ -118,6 +130,10 @@
     aiTools4: IconAiTools4,
     alert: IconAlert,
     arrowRight: IconArrowRight,
+    arrowRight1: IconArrowRight1,
+    case: IconCase,
+    code: IconCode,
+    comment: IconComment,
     copy: IconCopy,
     debug: IconDebug,
     delete: IconDelete,
@@ -136,6 +152,7 @@
     mcpAvatar: IconMcpAvatar,
     mySpcae: IconMySpcae,
     offcialExample: IconOffcialExample,
+    ops: IconOps,
     organization: IconOrganization,
     params: IconParams,
     playground: IconPlayground,
@@ -163,6 +180,7 @@
     subscribeFilled: IconSubscribeFilled,
     subtract: IconSubtract,
     tag: IconTag,
+    test: IconTest,
     tool: IconTool,
     unfold: IconUnfold,
 };
Index: src/icons/mcp/Subscribe.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import type { SVGProps } from \"react\";\nconst SvgSubscribe = (props: SVGProps<SVGSVGElement>) => (\n    <svg\n        xmlns=\"http://www.w3.org/2000/svg\"\n        width=\"1em\"\n        height=\"1em\"\n        fill=\"none\"\n        viewBox=\"0 0 17 16\"\n        {...props}\n    >\n        <path\n            fill=\"#545454\"\n            d=\"m4.094 12.914 3.184-1.592.106-.049a1.99 1.99 0 0 1 1.566 0l.106.049 3.184 1.592V2.569H4.093zm9.406.863-.002.05a.728.728 0 0 1-1.005.621l-.045-.02-3.956-1.978a.73.73 0 0 0-.65 0l-3.956 1.977-.046.021a.728.728 0 0 1-1.005-.621l-.002-.05V2.036c0-.39.306-.707.69-.726l.038-.001h9.249c.385.02.69.338.69.727z\"\n        />\n    </svg>\n);\nexport default SvgSubscribe;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/Subscribe.tsx b/src/icons/mcp/Subscribe.tsx
--- a/src/icons/mcp/Subscribe.tsx	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/icons/mcp/Subscribe.tsx	(date 1755055936390)
@@ -9,7 +9,7 @@
         {...props}
     >
         <path
-            fill="#545454"
+            fill="currentColor"
             d="m4.094 12.914 3.184-1.592.106-.049a1.99 1.99 0 0 1 1.566 0l.106.049 3.184 1.592V2.569H4.093zm9.406.863-.002.05a.728.728 0 0 1-1.005.621l-.045-.02-3.956-1.978a.73.73 0 0 0-.65 0l-3.956 1.977-.046.021a.728.728 0 0 1-1.005-.621l-.002-.05V2.036c0-.39.306-.707.69-.726l.038-.001h9.249c.385.02.69.338.69.727z"
         />
     </svg>
Index: src/components/MCP/MCPCollectButton/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {StarFilled, StarOutlined} from '@ant-design/icons';\nimport {Button, ButtonProps, message} from '@panda-design/components';\nimport {MouseEvent} from 'react';\nimport {apiDeleteServerFavorite, apiPutServerFavorite} from '@/api/mcp';\n\ninterface Props {\n    serverId: number;\n    favorite: boolean;\n    refresh: () => void;\n    size?: ButtonProps['size'];\n}\n\nexport const MCPCollectButton = ({serverId, refresh, favorite, size}: Props) => {\n    const handleClick = async (e: MouseEvent) => {\n        e.stopPropagation();\n        e.preventDefault();\n        if (favorite) {\n            await apiDeleteServerFavorite({mcpServerId: serverId});\n        } else {\n            await apiPutServerFavorite({mcpServerId: serverId});\n        }\n        message.success(favorite ? '取消收藏成功' : '收藏成功');\n        refresh();\n    };\n    return (\n        <Button\n            onClick={handleClick}\n            type=\"text\"\n            size={size}\n            icon={\n                favorite\n                    ? <StarFilled style={{color: '#FFA400'}} />\n                    : <StarOutlined />\n            }\n        >\n            收藏\n        </Button>\n    );\n};\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPCollectButton/index.tsx b/src/components/MCP/MCPCollectButton/index.tsx
--- a/src/components/MCP/MCPCollectButton/index.tsx	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/components/MCP/MCPCollectButton/index.tsx	(date 1755056897882)
@@ -1,6 +1,6 @@
 import {StarFilled, StarOutlined} from '@ant-design/icons';
 import {Button, ButtonProps, message} from '@panda-design/components';
-import {MouseEvent} from 'react';
+import {MouseEvent, CSSProperties} from 'react';
 import {apiDeleteServerFavorite, apiPutServerFavorite} from '@/api/mcp';
 
 interface Props {
@@ -8,9 +8,22 @@
     favorite: boolean;
     refresh: () => void;
     size?: ButtonProps['size'];
+    style?: CSSProperties;
+    showText?: boolean;
+    iconColor?: string;
+    className?: string;
 }
 
-export const MCPCollectButton = ({serverId, refresh, favorite, size}: Props) => {
+export const MCPCollectButton = ({
+    serverId,
+    refresh,
+    favorite,
+    size,
+    style,
+    showText = true,
+    iconColor,
+    className,
+}: Props) => {
     const handleClick = async (e: MouseEvent) => {
         e.stopPropagation();
         e.preventDefault();
@@ -27,13 +40,15 @@
             onClick={handleClick}
             type="text"
             size={size}
+            style={style}
+            className={className}
             icon={
                 favorite
                     ? <StarFilled style={{color: '#FFA400'}} />
-                    : <StarOutlined />
+                    : <StarOutlined style={{color: iconColor}} />
             }
         >
-            收藏
+            {showText ? '收藏' : undefined}
         </Button>
     );
 };
Index: src/components/MCP/TagGroup.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {Flex, Tooltip} from 'antd';\nimport {Tag, TagColor} from '@panda-design/components';\nimport {CSSProperties, ReactNode} from 'react';\nimport {IconTag} from '@/icons/mcp';\n\ninterface Label {\n    id: number;\n    label: string;\n}\nconst TooltipTitleWrapper = styled.div`\n    display: flex;\n    flex-wrap: wrap;\n    gap: 8px;\n    padding: 4px 0;\n`;\n\nconst COLOR_MAP: Record<string, CSSProperties> = {\n    'light-purple': {\n        backgroundColor: '#EBE6F9',\n        color: '#8264DE',\n    },\n    'gray': {\n        backgroundColor: '#E2E8F0',\n        color: '#4B6C9F',\n    },\n};\n\ninterface Props {\n    prefix?: ReactNode;\n    color?: TagColor;\n    labels: Label[];\n    maxNum?: number;\n    style?: CSSProperties;\n    gap?: number;\n}\n\nconst TagGroup = ({prefix = <IconTag />, labels, maxNum = 2, color = 'info', style, gap = 8}: Props) => {\n    const colorStyle = COLOR_MAP[color];\n    const innerColor = COLOR_MAP[color] ? undefined : color;\n    return (\n        <Flex style={{zIndex: 1, gap, ...style}} align=\"center\">\n            {prefix}\n            {\n                labels.slice(0, maxNum).map(label => {\n                    return (\n                        <Tag\n                            type=\"flat\"\n                            color={innerColor}\n                            key={label.id}\n                            style={{\n                                margin: 0,\n                                flexShrink: 1,\n                                overflow: 'hidden',\n                                textOverflow: 'ellipsis',\n                                ...colorStyle,\n                            }}\n                        >\n                            {label.label}\n                        </Tag>\n                    );\n                })\n            }\n            {\n                labels.length > maxNum && (\n                    <Tooltip\n                        title={\n                            <TooltipTitleWrapper>\n                                {labels.slice(maxNum).map(label => {\n                                    return (\n                                        <Tag\n                                            type=\"flat\"\n                                            color={innerColor}\n                                            key={label.id}\n                                            style={{\n                                                margin: 0,\n                                                ...colorStyle,\n                                            }}\n                                        >\n                                            {label.label}\n                                        </Tag>\n                                    );\n                                })}\n                            </TooltipTitleWrapper>\n                        }\n                    >\n                        <Tag\n                            type=\"flat\"\n                            color={innerColor}\n                            style={{\n                                margin: 0,\n                                ...colorStyle,\n                            }}\n                        >\n                            +{labels.length - maxNum}\n                        </Tag>\n                    </Tooltip>\n                )\n            }\n        </Flex>\n    );\n};\n\nexport default TagGroup;\n\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/TagGroup.tsx b/src/components/MCP/TagGroup.tsx
--- a/src/components/MCP/TagGroup.tsx	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/components/MCP/TagGroup.tsx	(date 1755004212405)
@@ -39,10 +39,10 @@
     const colorStyle = COLOR_MAP[color];
     const innerColor = COLOR_MAP[color] ? undefined : color;
     return (
-        <Flex style={{zIndex: 1, gap, ...style}} align="center">
+        <Flex style={{zIndex: 1, gap, height: 22, ...style}} align="center">
             {prefix}
             {
-                labels.slice(0, maxNum).map(label => {
+                labels.length > 0 ? labels.slice(0, maxNum).map(label => {
                     return (
                         <Tag
                             type="flat"
@@ -59,7 +59,19 @@
                             {label.label}
                         </Tag>
                     );
-                })
+                }) : (
+                    <Tag
+                        type="flat"
+                        color={innerColor}
+                        style={{
+                            margin: 0,
+                            opacity: 0,
+                            ...colorStyle,
+                        }}
+                    >
+                        占位标签
+                    </Tag>
+                )
             }
             {
                 labels.length > maxNum && (
Index: src/icons/mcp/test.svg
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/test.svg b/src/icons/mcp/test.svg
new file mode 100644
--- /dev/null	(date 1755004212355)
+++ b/src/icons/mcp/test.svg	(date 1755004212355)
@@ -0,0 +1,3 @@
+<svg width="19" height="20" viewBox="0 0 19 20" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M8.2504 16.0971C8.71741 16.8693 9.34198 17.535 10.0836 18.0509V18.0527H10.0854C10.7371 18.5052 11.4786 18.8417 12.276 19.0282C11.3549 19.6471 10.2953 20 9.16698 20C6.91036 20 4.92597 18.5893 3.78208 16.4555C2.98741 16.4628 2.51903 16.632 2.30822 16.8971C2.01583 17.2655 1.8591 17.6907 1.83343 18.1999C1.82729 18.3199 1.79752 18.4375 1.74581 18.546C1.6941 18.6545 1.62146 18.7518 1.53205 18.8324C1.44264 18.9129 1.33821 18.975 1.22471 19.0153C1.11121 19.0556 0.990873 19.0732 0.870567 19.067C0.75026 19.0609 0.632341 19.0312 0.523541 18.9796C0.414741 18.928 0.317192 18.8556 0.236463 18.7664C0.155734 18.6772 0.0934066 18.5731 0.0530387 18.4599C0.0126707 18.3467 -0.00494683 18.2266 0.00119188 18.1066C0.0470208 17.2226 0.337576 16.4327 0.870108 15.7616C1.36506 15.1381 2.11207 14.7861 3.07265 14.67C2.93666 14.1699 2.84344 13.6591 2.79401 13.1433H0.917771C0.797392 13.1433 0.67819 13.1196 0.56697 13.0737C0.45575 13.0278 0.354691 12.9604 0.269565 12.8755C0.184438 12.7906 0.116912 12.6899 0.0708408 12.5789C0.02477 12.468 0.00105753 12.3491 0.00105753 12.229C0.00105753 12.109 0.02477 11.9901 0.0708408 11.8791C0.116912 11.7682 0.184438 11.6674 0.269565 11.5825C0.354691 11.4976 0.45575 11.4303 0.56697 11.3844C0.67819 11.3384 0.797392 11.3148 0.917771 11.3148H2.79492C2.85316 10.7046 2.97292 10.1018 3.15239 9.51559C2.33297 9.36749 1.68861 9.02648 1.24591 8.4688C0.712457 7.79775 0.420985 7.00786 0.376073 6.1238C0.363675 5.88157 0.448256 5.64435 0.61121 5.46433C0.774163 5.2843 1.00214 5.17622 1.24499 5.16385C1.48784 5.15149 1.72566 5.23585 1.90615 5.39839C2.08664 5.56092 2.195 5.78832 2.2074 6.03054C2.23306 6.53977 2.39071 6.96489 2.68218 7.33332C2.87008 7.57011 3.26421 7.7301 3.91315 7.76667C4.33905 7.02266 4.89357 6.35967 5.55108 5.80839C5.26379 5.17435 5.14241 4.478 5.19829 3.78446C5.25417 3.09092 5.48549 2.42284 5.87061 1.84271C6.25573 1.26258 6.78207 0.789355 7.40041 0.467286C8.01874 0.145219 8.70887 -0.0151674 9.40624 0.00112877C10.1036 0.0174249 10.7855 0.209871 11.388 0.560468C11.9905 0.911065 12.4941 1.40836 12.8515 2.00584C13.209 2.60332 13.4087 3.28147 13.4319 3.97686C13.4552 4.67226 13.3013 5.36217 12.9845 5.98209C13.1339 6.1174 13.2778 6.2591 13.4172 6.40721C12.6607 6.44533 11.917 6.61746 11.221 6.91552C10.8717 6.68574 10.4877 6.51349 10.0836 6.40538V7.54634C9.34198 8.06223 8.71741 8.72786 8.2504 9.50005V6.40538C6.15877 6.95575 4.58408 9.35378 4.58408 12.229C4.58408 15.1043 6.15877 17.5023 8.2504 18.0527V16.0962V16.0971ZM15.9332 6.7793C16.0221 6.55074 16.0734 6.30298 16.0871 6.03054C16.0995 5.78832 16.2079 5.56092 16.3884 5.39839C16.5689 5.23585 16.8067 5.15149 17.0496 5.16385C17.2924 5.17622 17.5204 5.2843 17.6833 5.46433C17.8463 5.64435 17.9309 5.88157 17.9185 6.1238C17.8953 6.64956 17.7722 7.16614 17.5555 7.64599C17.0594 7.28065 16.513 6.98873 15.9332 6.7793ZM7.14134 4.85301C7.82687 4.57561 8.56231 4.44194 9.30188 4.46034C10.0414 4.47875 10.7693 4.64882 11.4401 4.95998C11.5763 4.61774 11.6276 4.24774 11.5896 3.88147C11.5516 3.5152 11.4254 3.16352 11.2219 2.85634C11.0183 2.54917 10.7433 2.2956 10.4204 2.11722C10.0974 1.93884 9.73602 1.84093 9.36697 1.83183C8.99792 1.82272 8.63212 1.90269 8.30072 2.06493C7.96931 2.22716 7.68211 2.46686 7.46356 2.76362C7.24501 3.06039 7.10158 3.40542 7.04548 3.76937C6.98938 4.13331 7.02226 4.50539 7.14134 4.85393V4.85301ZM17.2924 15.8201L18.814 17.7629C18.9594 17.9543 19.0234 18.1951 18.9923 18.4332C18.9612 18.6713 18.8374 18.8877 18.6476 19.0355C18.4579 19.1833 18.2175 19.2507 17.9783 19.2232C17.7391 19.1957 17.5204 19.0754 17.3694 18.8883L15.9295 17.0507C14.9517 17.6266 13.7945 17.82 12.6819 17.5933C11.5694 17.3666 10.5808 16.736 9.90787 15.8238C9.23489 14.9115 8.92542 13.7825 9.03942 12.6557C9.15342 11.5288 9.68278 10.4843 10.5249 9.72449C11.367 8.96474 12.462 8.54386 13.5975 8.54345C14.733 8.54303 15.8282 8.9631 16.6709 9.72223C17.5136 10.4814 18.0437 11.5255 18.1586 12.6523C18.2734 13.7791 17.9647 14.9083 17.2924 15.821V15.8201ZM13.2632 15.8366C13.6234 15.8844 13.9896 15.8604 14.3405 15.7662C14.6914 15.6719 15.0201 15.5092 15.3076 15.2875C15.5951 15.0657 15.8357 14.7893 16.0154 14.4743C16.1952 14.1592 16.3105 13.8118 16.3548 13.452C16.3991 13.0922 16.3714 12.7272 16.2734 12.3782C16.1754 12.0291 16.009 11.7029 15.7838 11.4184C15.5586 11.1339 15.2791 10.8967 14.9615 10.7206C14.6438 10.5445 14.2943 10.4329 13.9332 10.3923C13.2132 10.3115 12.4904 10.5173 11.9217 10.9651C11.3531 11.4129 10.9845 12.0664 10.8963 12.7836C10.808 13.5009 11.0071 14.2239 11.4503 14.7956C11.8935 15.3673 12.545 15.7414 13.2632 15.8366Z" fill="black"/>
+</svg>
Index: src/icons/mcp/code.svg
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/code.svg b/src/icons/mcp/code.svg
new file mode 100644
--- /dev/null	(date 1755004212366)
+++ b/src/icons/mcp/code.svg	(date 1755004212366)
@@ -0,0 +1,3 @@
+<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M18.3335 1.66602C18.5545 1.66602 18.7665 1.75381 18.9228 1.91009C19.079 2.06637 19.1668 2.27834 19.1668 2.49935V17.4993C19.1668 17.7204 19.079 17.9323 18.9228 18.0886C18.7665 18.2449 18.5545 18.3327 18.3335 18.3327H1.66683C1.44582 18.3327 1.23385 18.2449 1.07757 18.0886C0.921293 17.9323 0.833496 17.7204 0.833496 17.4993V2.49935C0.833496 2.27834 0.921293 2.06637 1.07757 1.91009C1.23385 1.75381 1.44582 1.66602 1.66683 1.66602H18.3335ZM17.5002 3.33268H2.50016V16.666H17.5002V3.33268ZM10.2772 5.42018L11.0168 5.50268C11.0721 5.50884 11.1255 5.52599 11.174 5.55311C11.2225 5.58024 11.2651 5.6168 11.2993 5.66063C11.3334 5.70447 11.3585 5.75469 11.373 5.80835C11.3874 5.86202 11.391 5.91803 11.3835 5.9731L10.3127 13.8035C10.2979 13.9111 10.2417 14.0086 10.1561 14.0754C10.0705 14.1422 9.96224 14.1729 9.85433 14.161L9.11475 14.0789C9.05951 14.0728 9.00607 14.0556 8.95756 14.0285C8.90905 14.0014 8.86646 13.9648 8.83229 13.921C8.79813 13.8771 8.77307 13.8269 8.75861 13.7733C8.74415 13.7196 8.74057 13.6636 8.74808 13.6085L9.81891 5.77768C9.83368 5.67013 9.88984 5.57258 9.97544 5.50581C10.061 5.43904 10.1693 5.40832 10.2772 5.42018ZM7.12058 7.48268L7.64308 7.96768C7.68386 8.00552 7.71664 8.05114 7.73951 8.10184C7.76238 8.15255 7.77486 8.20733 7.77622 8.26294C7.77758 8.31855 7.76779 8.37387 7.74743 8.42563C7.72706 8.4774 7.69654 8.52457 7.65766 8.56435L6.22016 10.0343L7.65766 11.5043C7.7349 11.5834 7.77759 11.6898 7.77634 11.8003C7.77508 11.9108 7.73 12.0163 7.651 12.0935L7.64308 12.101L7.12058 12.586C7.04138 12.6596 6.9367 12.6995 6.82864 12.6973C6.72057 12.6951 6.61758 12.651 6.54141 12.5744L4.30975 10.3281C4.23214 10.25 4.18858 10.1444 4.18858 10.0343C4.18858 9.92427 4.23214 9.81867 4.30975 9.7406L6.54141 7.49435C6.61758 7.41766 6.72057 7.37358 6.82864 7.3714C6.9367 7.36923 7.04138 7.40913 7.12058 7.48268ZM13.4589 7.49435L15.6906 9.7406C15.7682 9.81867 15.8117 9.92427 15.8117 10.0343C15.8117 10.1444 15.7682 10.25 15.6906 10.3281L13.4589 12.5739C13.3829 12.6507 13.2799 12.695 13.1719 12.6973C13.0638 12.6996 12.9591 12.6599 12.8797 12.5864L12.3572 12.101C12.3165 12.0632 12.2837 12.0176 12.2608 11.9669C12.2379 11.9161 12.2255 11.8614 12.2241 11.8058C12.2227 11.7501 12.2325 11.6948 12.2529 11.6431C12.2733 11.5913 12.3038 11.5441 12.3427 11.5043L13.7802 10.0343L12.3427 8.56435C12.3038 8.52457 12.2733 8.4774 12.2529 8.42563C12.2325 8.37387 12.2227 8.31855 12.2241 8.26294C12.2255 8.20733 12.2379 8.15255 12.2608 8.10184C12.2837 8.05114 12.3165 8.00552 12.3572 7.96768L12.8797 7.48268C12.9589 7.40913 13.0636 7.36923 13.1717 7.3714C13.2798 7.37358 13.3827 7.41766 13.4589 7.49435Z" fill="black"/>
+</svg>
Index: src/icons/mcp/Comment.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/Comment.tsx b/src/icons/mcp/Comment.tsx
new file mode 100644
--- /dev/null	(date 1755004212391)
+++ b/src/icons/mcp/Comment.tsx	(date 1755004212391)
@@ -0,0 +1,17 @@
+import type { SVGProps } from "react";
+const SvgComment = (props: SVGProps<SVGSVGElement>) => (
+    <svg
+        xmlns="http://www.w3.org/2000/svg"
+        width="1em"
+        height="1em"
+        fill="none"
+        viewBox="0 0 14 14"
+        {...props}
+    >
+        <path
+            fill="#000"
+            d="M11.667 1.166c.966 0 1.75.803 1.75 1.795v6.58c0 .992-.784 1.795-1.75 1.795H6.846l-2.793 1.432a.582.582 0 0 1-.84-.466l-.005-.068v-.898h-.875c-.932 0-1.694-.747-1.747-1.689l-.003-.105V2.96c0-.992.784-1.795 1.75-1.795zm0 1.196H2.334a.59.59 0 0 0-.584.599v6.58c0 .33.261.599.583.599h1.459a.59.59 0 0 1 .583.598v.527l2.073-1.062q.09-.045.193-.059l.067-.004h4.959a.59.59 0 0 0 .583-.598V2.96a.59.59 0 0 0-.583-.599M4.084 5.204c.299 0 .546.231.579.529l.004.07V6.7a.59.59 0 0 1-.583.598.59.59 0 0 1-.58-.528L3.5 6.7v-.897a.59.59 0 0 1 .583-.598m2.916 0c.3 0 .546.231.58.529l.003.07V6.7A.59.59 0 0 1 7 7.298a.59.59 0 0 1-.58-.528l-.003-.07v-.897A.59.59 0 0 1 7 5.205m2.917 0c.3 0 .546.231.58.529l.003.07V6.7c0 .33-.261.598-.583.598a.59.59 0 0 1-.58-.528l-.003-.07v-.897a.59.59 0 0 1 .583-.598"
+        />
+    </svg>
+);
+export default SvgComment;
Index: src/constants/colors/myColors.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>// 以下颜色是自定义颜色，没有限制，可以自行添加\n// 如果有非常相近的颜色，可以和视觉同学确认下能不能统一\n// 在添加颜色之前，先确认一下 token 里面有没有对应的颜色，比如 colorPrimaryBg 之类的\n// token 对应的规范可以看 https://panda-design-team.github.io/\nexport const myColors = {\n    aiBg: '#8c66ff1a',\n    steelBlueLight: '#e9ebef',\n    muted: '#f8fafc',\n    textBlue: '#60a5fa',\n    darkGray: '#4b5563',\n    lightGray: '#9ca3af',\n    whiteTransparent: 'rgba(255, 255, 255, 0.5)',\n    blueGray: '#4b6c9f',\n    lightBlueGray: '#4b6c9f26',\n    primaryTransparent40: '#0080ff66',\n    lightBlueTransparent15: '#8dc9fe26',\n    lightBlueTransparent30: '#8dc9fe4d',\n    legacyPrimary: '#0080ff',\n    primaryFocus: '#cce5ff',\n    primaryBg: '#e5f2ff',\n    primaryBgTransparent: '#e5f2ff33',\n    purple: '#8862e6',\n    purpleTransparent: '#8862e626',\n    success: '#00aa5b',\n    successTransparent: '#00cc6d1a',\n    yellow: '#f58300',\n    yellowTransparent: '#f583001a',\n    infoTransparent15: '#317ff526',\n    infoTransparent20: '#317ff533',\n    // 从 icode 迁入\n    aiGradientBg1: '#f5faff',\n    aiGradientBg2: '#fff3e8',\n    infoShadowColor: '#108cee26',\n    // 一部分透明色\n    blackTransparent60: 'rgba(0, 0, 0, 60%)',\n    blackTransparent30: 'rgba(0, 0, 0, 30%)',\n    blackTransparent15: 'rgba(0, 0, 0, 15%)',\n    blackTransparent10: 'rgba(0, 0, 0, 10%)',\n    whiteTransparent60: 'rgba(255, 255, 255, 60%)',\n    redTransparent50: 'rgba(249, 213, 213, 50%)',\n    blueTransparent60: 'rgba(191, 216, 246, 60%)',\n    primaryLegacyTransparent5: '#108cee0d',\n};\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/constants/colors/myColors.ts b/src/constants/colors/myColors.ts
--- a/src/constants/colors/myColors.ts	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/constants/colors/myColors.ts	(date 1755004212438)
@@ -24,6 +24,10 @@
     success: '#00aa5b',
     successTransparent: '#00cc6d1a',
     yellow: '#f58300',
+    // MCP评分颜色
+    ratingYellow: '#f58300',
+    ratingOrange: '#ff8c00',
+    ratingRed: '#e62c4b',
     yellowTransparent: '#f583001a',
     infoTransparent15: '#317ff526',
     infoTransparent20: '#317ff533',
Index: src/icons/mcp/Ops.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/Ops.tsx b/src/icons/mcp/Ops.tsx
new file mode 100644
--- /dev/null	(date 1755004212422)
+++ b/src/icons/mcp/Ops.tsx	(date 1755004212422)
@@ -0,0 +1,17 @@
+import type { SVGProps } from "react";
+const SvgOps = (props: SVGProps<SVGSVGElement>) => (
+    <svg
+        xmlns="http://www.w3.org/2000/svg"
+        width="1em"
+        height="1em"
+        fill="none"
+        viewBox="0 0 20 20"
+        {...props}
+    >
+        <path
+            fill="#000"
+            d="M1.947 1.946c1.105-.682 2.58-.314 3.211.84a2.277 2.277 0 0 1-.843 3.2 2.53 2.53 0 0 1-1.841.21c-1.264 2.414-1.263 5.457.21 7.975 1.843 3.148 5.421 4.67 8.842 4.04.421-.052.79.158.895.63s-.158.892-.632.997c-4.052.734-8.316-1.05-10.474-4.827-1.789-3.096-1.683-6.769-.104-9.707-.053-.053-.053-.105-.106-.157a2.276 2.276 0 0 1 .842-3.2M8.211.162c4-.734 8.263 1.05 10.474 4.827 1.789 3.096 1.683 6.77.104 9.707.053.053.053.105.105.105a2.276 2.276 0 0 1-.841 3.2c-1.106.682-2.58.316-3.211-.839a2.277 2.277 0 0 1 .842-3.2 2.53 2.53 0 0 1 1.842-.21c1.263-2.414 1.263-5.457-.21-7.975-1.843-3.148-5.421-4.67-8.842-4.04a.82.82 0 0 1-.895-.578c-.105-.472.158-.892.632-.997m9.315 15.479c-.21-.367-.684-.473-1.052-.263-.369.21-.474.683-.263 1.05s.683.471 1.052.262c.368-.21.474-.682.263-1.05M9.736 4.085a.45.45 0 0 1 .527 0l2.369 1.467a.6.6 0 0 1 .263.507v1.974c0 .226.105.395.263.508l1.578.96a.6.6 0 0 1 .264.507v2.934a.6.6 0 0 1-.264.507l-2.368 1.466a.45.45 0 0 1-.526 0L10 13.787l-1.842 1.128a.45.45 0 0 1-.526 0l-2.37-1.466A.6.6 0 0 1 5 12.942v-2.934c0-.226.105-.395.263-.508l1.579-.959a.6.6 0 0 0 .263-.508V6.06c0-.226.106-.394.263-.507zm-3.157 6.6v1.635l1.315.79 1.317-.79v-1.635l-1.317-.79zm4.21 0v1.635l1.316.79 1.316-.79v-1.635l-1.316-.79zM8.685 6.736v1.636l1.315.79 1.315-.79V6.736L10 5.946zM3.789 3.521c-.21-.368-.684-.473-1.053-.263s-.473.683-.262 1.05.684.471 1.052.261c.369-.21.474-.681.263-1.048"
+        />
+    </svg>
+);
+export default SvgOps;
Index: src/icons/mcp/case.svg
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/case.svg b/src/icons/mcp/case.svg
new file mode 100644
--- /dev/null	(date 1755004212324)
+++ b/src/icons/mcp/case.svg	(date 1755004212324)
@@ -0,0 +1,3 @@
+<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M0.533333 12.7992H15.4667C15.6081 12.7992 15.7438 12.743 15.8438 12.643C15.9438 12.543 16 12.4073 16 12.2659C16 12.1244 15.9438 11.9888 15.8438 11.8888C15.7438 11.7887 15.6081 11.7326 15.4667 11.7326H0.533333C0.391885 11.7326 0.256229 11.7887 0.15621 11.8888C0.0561903 11.9888 0 12.1244 0 12.2659C0 12.4073 0.0561903 12.543 0.15621 12.643C0.256229 12.743 0.391885 12.7992 0.533333 12.7992ZM3.2 11.7326V4.79389C3.2 4.50802 3.4432 4.26589 3.73013 4.26589H12.2709C12.5632 4.26589 12.8 4.50269 12.8 4.79495V11.7326H13.8667V4.79442C13.8667 3.91389 13.1531 3.19922 12.2704 3.19922H3.72853C3.30607 3.2009 2.90138 3.36944 2.60261 3.66811C2.30383 3.96679 2.13516 4.37142 2.13333 4.79389V11.7326H3.2ZM6.44053 8.68189C6.48846 8.72957 6.547 8.76523 6.61136 8.78595C6.67571 8.80666 6.74406 8.81185 6.8108 8.80108C6.87755 8.79031 6.9408 8.7639 6.99537 8.72399C7.04995 8.68408 7.0943 8.63182 7.1248 8.57149L7.86453 7.08882L8.42347 9.32242C8.44396 9.40413 8.4882 9.47794 8.55062 9.53452C8.61304 9.59109 8.69083 9.6279 8.77416 9.64028C8.85748 9.65267 8.94261 9.64008 9.01879 9.6041C9.09496 9.56812 9.15876 9.51038 9.20213 9.43815L10.1781 7.81095L10.6299 8.26269C10.6687 8.30552 10.7158 8.34002 10.7683 8.36409C10.8209 8.38817 10.8778 8.40131 10.9355 8.40274C10.9933 8.40416 11.0508 8.39383 11.1045 8.37237C11.1581 8.35091 11.2069 8.31877 11.2477 8.2779C11.2886 8.23703 11.3208 8.18828 11.3422 8.13461C11.3637 8.08095 11.374 8.02348 11.3726 7.9657C11.3712 7.90792 11.358 7.85103 11.3339 7.79848C11.3099 7.74593 11.2754 7.69883 11.2325 7.66002L10.3963 6.82162C10.3504 6.77563 10.2947 6.74072 10.2333 6.71954C10.1719 6.69835 10.1065 6.69146 10.0421 6.69938C9.97761 6.7073 9.91582 6.72983 9.8614 6.76524C9.80697 6.80066 9.76135 6.84803 9.728 6.90375L9.0016 8.11442L8.41227 5.76295C8.39095 5.67864 8.34439 5.60285 8.2788 5.54574C8.21321 5.48863 8.13174 5.45292 8.04529 5.44341C7.95885 5.43389 7.87156 5.45102 7.79512 5.4925C7.71868 5.53398 7.65674 5.59782 7.6176 5.67549L6.6256 7.65949L6.20587 7.24082C6.14257 7.17754 6.06098 7.13576 5.97264 7.12141C5.8843 7.10705 5.79368 7.12084 5.7136 7.16082L4.8752 7.57948C4.82171 7.60245 4.77347 7.6361 4.73345 7.67838C4.69343 7.72065 4.66248 7.77066 4.64249 7.82534C4.62249 7.88001 4.61389 7.93819 4.61719 7.99631C4.6205 8.05443 4.63565 8.11126 4.66171 8.16331C4.68777 8.21537 4.7242 8.26154 4.76875 8.29901C4.81331 8.33648 4.86505 8.36444 4.9208 8.38119C4.97655 8.39794 5.03514 8.40311 5.09297 8.3964C5.15079 8.38969 5.20663 8.37123 5.25707 8.34215L5.8192 8.06055L6.43947 8.68242L6.44053 8.68189Z" fill="black"/>
+</svg>
Index: src/components/MCP/MCPSubscribeButton/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Button, ButtonProps, message} from '@panda-design/components';\nimport {MouseEvent, useCallback, CSSProperties, useEffect, useMemo} from 'react';\nimport {Popconfirm} from 'antd';\nimport {useBoolean} from 'huse';\nimport {loadAppListForMCPServer, setMCPSubscribe, useAppListForMCPServer} from '@/regions/mcp/mcpServer';\nimport {IconSubscribe} from '@/icons/mcp';\nimport {openMCPApplicationModal} from '@/regions/mcp/mcpApplication';\nimport {ApplicationBase} from '@/types/mcp/mcp';\nimport {IconSubscribeFilled} from '../../../icons/mcp/index';\nimport {useMCPWorkspaceId} from '../hooks';\n\ninterface Props {\n    showText?: boolean;\n    showCount?: boolean;\n    id: number;\n    workspaceId: number;\n    enableGlobalParams?: boolean;\n    refresh?: () => void;\n    style?: CSSProperties;\n    size?: ButtonProps['size'];\n}\n\nexport const MCPSubscribeButton = ({id, workspaceId, style, showText, showCount, size, enableGlobalParams}: Props) => {\n    const [open, {on, off}] = useBoolean(false);\n    const spaceId = useMCPWorkspaceId();\n    const appList = useAppListForMCPServer(id);\n\n    const apps = useMemo<ApplicationBase[]>(\n        () => appList?.reduce((acc, cur) => {\n            return [...acc, ...(spaceId && cur.workspaceId !== spaceId ? [] : cur.applications)];\n        }, []),\n        [appList, spaceId]\n    );\n    const subscribedNumber = useMemo(\n        () => (apps ?? []).filter(app => app.ifSub).length,\n        [apps]\n    );\n    const isSubscribed = subscribedNumber > 0;\n\n    const handleClick = useCallback(\n        async (e: MouseEvent) => {\n            e.stopPropagation();\n            try {\n                if (apps?.length) {\n                    setMCPSubscribe({\n                        serverId: id,\n                        workspaceId,\n                        enableGlobalParams: enableGlobalParams ?? false,\n                        onSuccess: () => loadAppListForMCPServer({mcpServerId: id}),\n                    });\n                }\n                else {\n                    on();\n                }\n            }\n            catch (e) {\n                message.error('服务器开小差了，请稍后重试');\n            }\n        },\n        [apps, id, workspaceId, enableGlobalParams, on]\n    );\n\n    const handleCancel = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            off();\n        },\n        [off]\n    );\n\n    const handleOk = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            off();\n            openMCPApplicationModal();\n        },\n        [off]\n    );\n\n\n    useEffect(\n        () => {\n            if (!spaceId && !apps?.length) {\n                loadAppListForMCPServer({mcpServerId: id});\n            }\n            if (spaceId) {\n                loadAppListForMCPServer({mcpServerId: id});\n            }\n        },\n        [apps?.length, id, spaceId]\n    );\n\n    return (\n        <Popconfirm\n            title=\"\"\n            description=\"您还没有可用应用，请先创建后订阅\"\n            open={open}\n            onConfirm={handleOk}\n            onCancel={handleCancel}\n            okText=\"立即创建\"\n            cancelText=\"稍后再说\"\n            placement=\"bottom\"\n        >\n            <Button\n                style={{padding: 0, gap: 3, ...style}}\n                onClick={handleClick}\n                icon={\n                    isSubscribed\n                        ? <IconSubscribeFilled style={{color: '#FFA400'}} />\n                        : <IconSubscribe />\n                }\n                type=\"text\"\n                size={size}\n            >\n                {showText\n                    ? '订阅' + (showCount ? ` ${subscribedNumber}` : '')\n                    : undefined\n                }\n            </Button>\n        </Popconfirm>\n    );\n};\n\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/MCPSubscribeButton/index.tsx b/src/components/MCP/MCPSubscribeButton/index.tsx
--- a/src/components/MCP/MCPSubscribeButton/index.tsx	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/components/MCP/MCPSubscribeButton/index.tsx	(date 1755056870738)
@@ -18,9 +18,21 @@
     refresh?: () => void;
     style?: CSSProperties;
     size?: ButtonProps['size'];
+    iconColor?: string;
+    className?: string;
 }
 
-export const MCPSubscribeButton = ({id, workspaceId, style, showText, showCount, size, enableGlobalParams}: Props) => {
+export const MCPSubscribeButton = ({
+    id,
+    workspaceId,
+    style,
+    showText,
+    showCount,
+    size,
+    enableGlobalParams,
+    iconColor,
+    className,
+}: Props) => {
     const [open, {on, off}] = useBoolean(false);
     const spaceId = useMCPWorkspaceId();
     const appList = useAppListForMCPServer(id);
@@ -104,10 +116,11 @@
             <Button
                 style={{padding: 0, gap: 3, ...style}}
                 onClick={handleClick}
+                className={className}
                 icon={
                     isSubscribed
                         ? <IconSubscribeFilled style={{color: '#FFA400'}} />
-                        : <IconSubscribe />
+                        : <IconSubscribe style={{color: iconColor}} />
                 }
                 type="text"
                 size={size}
Index: src/icons/mcp/Code.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/Code.tsx b/src/icons/mcp/Code.tsx
new file mode 100644
--- /dev/null	(date 1755004212334)
+++ b/src/icons/mcp/Code.tsx	(date 1755004212334)
@@ -0,0 +1,17 @@
+import type { SVGProps } from "react";
+const SvgCode = (props: SVGProps<SVGSVGElement>) => (
+    <svg
+        xmlns="http://www.w3.org/2000/svg"
+        width="1em"
+        height="1em"
+        fill="none"
+        viewBox="0 0 20 20"
+        {...props}
+    >
+        <path
+            fill="#000"
+            d="M18.334 1.666a.833.833 0 0 1 .833.833v15a.834.834 0 0 1-.834.834H1.668a.833.833 0 0 1-.834-.834v-15a.833.833 0 0 1 .834-.833zM17.5 3.333h-15v13.333h15zM10.277 5.42l.74.083a.416.416 0 0 1 .367.47l-1.071 7.83a.417.417 0 0 1-.459.358l-.74-.082a.416.416 0 0 1-.366-.47l1.07-7.831a.417.417 0 0 1 .46-.358M7.121 7.483l.522.485a.417.417 0 0 1 .015.596l-1.438 1.47 1.438 1.47a.417.417 0 0 1-.007.59l-.008.007-.522.485a.416.416 0 0 1-.58-.012L4.31 10.328a.417.417 0 0 1 0-.587L6.54 7.494a.417.417 0 0 1 .58-.011m6.338.011 2.232 2.247a.417.417 0 0 1 0 .587l-2.232 2.246a.416.416 0 0 1-.58.012l-.522-.485a.42.42 0 0 1-.014-.597l1.437-1.47-1.437-1.47a.417.417 0 0 1 .014-.596l.523-.485a.417.417 0 0 1 .579.011"
+        />
+    </svg>
+);
+export default SvgCode;
Index: src/icons/mcp/ops.svg
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/ops.svg b/src/icons/mcp/ops.svg
new file mode 100644
--- /dev/null	(date 1755004212582)
+++ b/src/icons/mcp/ops.svg	(date 1755004212582)
@@ -0,0 +1,3 @@
+<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M1.94721 1.94644C3.05247 1.26436 4.52657 1.632 5.15815 2.78628C5.842 3.88805 5.47312 5.35695 4.31538 5.98648C3.73649 6.30114 3.05243 6.35381 2.47358 6.19644C1.2105 8.6099 1.21089 11.6527 2.68452 14.171C4.52662 17.3191 8.10526 18.8407 11.5263 18.2111C11.9473 18.1587 12.3156 18.3688 12.4208 18.841C12.5261 19.3132 12.2627 19.7331 11.789 19.838C7.7365 20.5724 3.4732 18.7884 1.31538 15.0109C-0.474039 11.9153 -0.368047 8.24201 1.21088 5.30386C1.15828 5.2514 1.15804 5.19909 1.10541 5.14663C0.421318 4.04485 0.78941 2.57604 1.94721 1.94644ZM8.21088 0.162258C12.2107 -0.572061 16.4741 1.2119 18.6845 4.98941C20.4739 8.08497 20.3679 11.7583 18.789 14.6964C18.8416 14.7488 18.842 14.8008 18.8945 14.8009C19.5786 15.9027 19.2104 17.3714 18.0527 18.0011C16.9474 18.6832 15.4733 18.3165 14.8417 17.1623C14.1575 16.0604 14.5266 14.5907 15.6845 13.9611C16.2633 13.6465 16.9475 13.5938 17.5263 13.7511C18.7893 11.3377 18.789 8.29481 17.3154 5.77652C15.4732 2.62858 11.8946 1.10688 8.47358 1.73648C8.05253 1.78894 7.68431 1.5266 7.57905 1.15933C7.47381 0.687138 7.73721 0.267189 8.21088 0.162258ZM17.5263 15.6408C17.3158 15.2735 16.842 15.1682 16.4736 15.3781C16.1052 15.588 16.0004 16.0606 16.2109 16.4279C16.4215 16.7947 16.8944 16.8992 17.2626 16.6896C17.631 16.4798 17.7366 16.008 17.5263 15.6408ZM9.73627 4.08511C9.89412 3.97232 10.1048 3.97239 10.2626 4.08511L12.6318 5.55191C12.7894 5.66466 12.8943 5.8335 12.8945 6.05874V8.03335C12.8945 8.25899 13.0003 8.42834 13.1581 8.54116L14.7363 9.50015C14.8942 9.61297 14.9999 9.78232 14.9999 10.008V12.9416C14.9998 13.167 14.8941 13.3366 14.7363 13.4494L12.3681 14.9152C12.2102 15.028 11.9996 15.028 11.8417 14.9152L9.99995 13.7873L8.15815 14.9152C8.00026 15.028 7.78968 15.028 7.63178 14.9152L5.26264 13.4494C5.10487 13.3366 5.00005 13.167 4.99995 12.9416V10.008C4.99995 9.78245 5.1049 9.61297 5.26264 9.50015L6.84174 8.54116C6.99964 8.42834 7.10541 8.25899 7.10541 8.03335V6.05874C7.10554 5.83348 7.21051 5.66466 7.36811 5.55191L9.73627 4.08511ZM6.57905 10.6847V12.3205L7.89448 13.1105L9.21088 12.3205V10.6847L7.89448 9.89468L6.57905 10.6847ZM10.789 10.6847V12.3205L12.1054 13.1105L13.4208 12.3205V10.6847L12.1054 9.89468L10.789 10.6847ZM8.68452 6.73648V8.37222L9.99995 9.16128L11.3154 8.37222V6.73648L9.99995 5.94644L8.68452 6.73648ZM3.78901 3.52066C3.57848 3.15339 3.10469 3.04809 2.73627 3.25796C2.3681 3.46792 2.26311 3.94059 2.47358 4.30777C2.68417 4.67486 3.15797 4.77931 3.52631 4.56948C3.89458 4.35956 3.9995 3.88786 3.78901 3.52066Z" fill="black"/>
+</svg>
Index: src/icons/mcp/comment.svg
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/comment.svg b/src/icons/mcp/comment.svg
new file mode 100644
--- /dev/null	(date 1755004212598)
+++ b/src/icons/mcp/comment.svg	(date 1755004212598)
@@ -0,0 +1,3 @@
+<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M11.6668 1.16602C12.6334 1.16602 13.4168 1.96927 13.4168 2.96093V9.54152C13.4168 10.5332 12.6334 11.3364 11.6668 11.3364H6.84558L4.05258 12.7685C3.68508 12.9569 3.25633 12.7073 3.21258 12.3024L3.2085 12.2336V11.3364H2.3335C1.40133 11.3364 0.639496 10.5892 0.586413 9.6471L0.583496 9.54152V2.96093C0.583496 1.96927 1.36691 1.16602 2.3335 1.16602H11.6668ZM11.6668 2.36243H2.3335C2.0115 2.36243 1.75016 2.63077 1.75016 2.96093V9.54152C1.75016 9.87168 2.0115 10.14 2.3335 10.14H3.79183C4.11383 10.14 4.37516 10.4078 4.37516 10.7379V11.2653L6.44775 10.203C6.50811 10.1721 6.57349 10.1522 6.64083 10.1441L6.7085 10.14H11.6668C11.9888 10.14 12.2502 9.87168 12.2502 9.54152V2.96093C12.2502 2.63018 11.9888 2.36243 11.6668 2.36243ZM4.0835 5.20443C4.38275 5.20443 4.6295 5.43543 4.66275 5.73293L4.66683 5.80293V6.7001C4.66683 7.03027 4.4055 7.29802 4.0835 7.29802C3.93892 7.29635 3.8 7.24158 3.69318 7.14412C3.58637 7.04667 3.51912 6.91334 3.50425 6.76952L3.50016 6.69952V5.80293C3.50016 5.47277 3.7615 5.20502 4.0835 5.20502V5.20443ZM7.00016 5.20443C7.29941 5.20443 7.54616 5.43543 7.57941 5.73293L7.5835 5.80293V6.7001C7.5835 7.03027 7.32216 7.29802 7.00016 7.29802C6.85558 7.29635 6.71666 7.24158 6.60985 7.14412C6.50304 7.04667 6.43579 6.91334 6.42091 6.76952L6.41683 6.69952V5.80293C6.41683 5.47277 6.67816 5.20502 7.00016 5.20502V5.20443ZM9.91683 5.20443C10.2161 5.20443 10.4628 5.43543 10.4961 5.73293L10.5002 5.80293V6.7001C10.5002 7.03027 10.2388 7.29802 9.91683 7.29802C9.77225 7.29635 9.63333 7.24158 9.52652 7.14412C9.4197 7.04667 9.35246 6.91334 9.33758 6.76952L9.3335 6.69952V5.80293C9.3335 5.47277 9.59483 5.20502 9.91683 5.20502V5.20443Z" fill="black"/>
+</svg>
Index: src/components/MCP/PublishInfo.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, Typography} from 'antd';\nimport styled from '@emotion/styled';\nimport {css, cx} from '@emotion/css';\nimport {CSSProperties, useMemo} from 'react';\nimport UserAvatar from '@/design/UserAvatar';\nimport {overflowHiddenCss} from '@/styles/components';\nimport {howLongAgo} from '@/utils/date';\n\nconst Text = styled(Typography.Text)`\n    font-size: 12px;\n    line-height: 20px;\n`;\n\nconst flexZero = css`\n    flex-shrink: 0;\n    flex-grow: 0;\n`;\ninterface Props {\n    className?: string;\n    color?: string;\n    username?: string;\n    time?: string | number | Date;\n    style?: CSSProperties;\n}\n\nexport default function PublishInfo({className, username, time, color = '#545454', style}: Props) {\n    const timeStr = useMemo(\n        () => {\n            try {\n                return howLongAgo(time);\n            } catch (e) {\n                return '0秒';\n            }\n        },\n        [time]\n    );\n    return (\n        <Flex className={cx(overflowHiddenCss, className)} style={style} align=\"center\" gap={4}>\n            <UserAvatar username={username} iconSize={18} className={flexZero} />\n            <Text ellipsis style={{color}}>\n                {username} 发布于 {timeStr}前\n            </Text>\n        </Flex>\n    );\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/PublishInfo.tsx b/src/components/MCP/PublishInfo.tsx
--- a/src/components/MCP/PublishInfo.tsx	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/components/MCP/PublishInfo.tsx	(date 1755004212555)
@@ -1,8 +1,7 @@
 import {Flex, Typography} from 'antd';
 import styled from '@emotion/styled';
-import {css, cx} from '@emotion/css';
+import {cx} from '@emotion/css';
 import {CSSProperties, useMemo} from 'react';
-import UserAvatar from '@/design/UserAvatar';
 import {overflowHiddenCss} from '@/styles/components';
 import {howLongAgo} from '@/utils/date';
 
@@ -11,10 +10,6 @@
     line-height: 20px;
 `;
 
-const flexZero = css`
-    flex-shrink: 0;
-    flex-grow: 0;
-`;
 interface Props {
     className?: string;
     color?: string;
@@ -36,7 +31,6 @@
     );
     return (
         <Flex className={cx(overflowHiddenCss, className)} style={style} align="center" gap={4}>
-            <UserAvatar username={username} iconSize={18} className={flexZero} />
             <Text ellipsis style={{color}}>
                 {username} 发布于 {timeStr}前
             </Text>
Index: src/icons/mcp/Test.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/Test.tsx b/src/icons/mcp/Test.tsx
new file mode 100644
--- /dev/null	(date 1755004212668)
+++ b/src/icons/mcp/Test.tsx	(date 1755004212668)
@@ -0,0 +1,17 @@
+import type { SVGProps } from "react";
+const SvgTest = (props: SVGProps<SVGSVGElement>) => (
+    <svg
+        xmlns="http://www.w3.org/2000/svg"
+        width="1em"
+        height="1em"
+        fill="none"
+        viewBox="0 0 19 20"
+        {...props}
+    >
+        <path
+            fill="#000"
+            d="M8.25 16.097a6.4 6.4 0 0 0 1.834 1.954v.002h.001a6.4 6.4 0 0 0 2.191.975c-.921.62-1.98.972-3.109.972-2.257 0-4.241-1.41-5.385-3.544-.795.007-1.263.176-1.474.441a2.2 2.2 0 0 0-.475 1.303.913.913 0 0 1-.962.867.92.92 0 0 1-.87-.96 4.03 4.03 0 0 1 .87-2.345c.494-.624 1.241-.976 2.202-1.092q-.205-.75-.279-1.527H.918a.92.92 0 0 1-.847-.564.912.912 0 0 1 .847-1.264h1.877a9 9 0 0 1 .357-1.8c-.819-.148-1.463-.489-1.906-1.046a4 4 0 0 1-.87-2.345.913.913 0 0 1 .869-.96.92.92 0 0 1 .962.867c.026.509.184.934.475 1.302.188.237.582.397 1.231.434a7.3 7.3 0 0 1 1.638-1.959 4.105 4.105 0 0 1 1.85-5.34 4.133 4.133 0 0 1 5.45 1.538 4.1 4.1 0 0 1 .133 3.976q.225.203.433.425c-.756.038-1.5.21-2.196.509a3.9 3.9 0 0 0-1.137-.51v1.14A6.4 6.4 0 0 0 8.25 9.5V6.405c-2.091.55-3.666 2.949-3.666 5.824s1.575 5.273 3.666 5.824zm7.683-9.318q.134-.341.154-.748a.914.914 0 0 1 .963-.867.92.92 0 0 1 .869.96 4.1 4.1 0 0 1-.364 1.522 6.4 6.4 0 0 0-1.622-.867M7.141 4.853a5.4 5.4 0 0 1 4.3.107 2.28 2.28 0 0 0-1.02-2.843 2.296 2.296 0 0 0-2.957.647 2.28 2.28 0 0 0-.323 2.09zM17.292 15.82l1.522 1.943a.913.913 0 0 1-.836 1.46.92.92 0 0 1-.609-.335l-1.44-1.837a4.593 4.593 0 0 1-6.021-1.227 4.563 4.563 0 0 1 .617-6.1 4.59 4.59 0 0 1 6.146-.002 4.563 4.563 0 0 1 .622 6.099m-4.029.017a2.76 2.76 0 0 0 2.752-1.363 2.737 2.737 0 0 0-1.054-3.753 2.76 2.76 0 0 0-3.04.244 2.741 2.741 0 0 0 1.342 4.872"
+        />
+    </svg>
+);
+export default SvgTest;
Index: src/icons/mcp/arrowRight1.svg
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/arrowRight1.svg b/src/icons/mcp/arrowRight1.svg
new file mode 100644
--- /dev/null	(date 1755004212457)
+++ b/src/icons/mcp/arrowRight1.svg	(date 1755004212457)
@@ -0,0 +1,3 @@
+<svg width="14" height="10" viewBox="0 0 14 10" fill="none" xmlns="http://www.w3.org/2000/svg">
+<path d="M13.444 5.40362C13.5444 5.29318 13.6 5.14928 13.6 5.00002C13.6 4.85077 13.5444 4.70687 13.444 4.59642L9.444 0.196424C9.391 0.138117 9.32703 0.0908205 9.25575 0.0572348C9.18447 0.023649 9.10727 0.00443194 9.02857 0.000680454C8.94986 -0.00307103 8.87119 0.00871668 8.79703 0.0353706C8.72288 0.0620245 8.65471 0.103022 8.5964 0.156024C8.53809 0.209026 8.4908 0.272993 8.45721 0.344273C8.42362 0.415553 8.40441 0.49275 8.40066 0.571457C8.3969 0.650164 8.40869 0.728839 8.43535 0.80299C8.462 0.877142 8.503 0.945317 8.556 1.00362L11.644 4.40002H0.6C0.44087 4.40002 0.288258 4.46324 0.175736 4.57576C0.063214 4.68828 0 4.84089 0 5.00002C0 5.15915 0.063214 5.31177 0.175736 5.42429C0.288258 5.53681 0.44087 5.60002 0.6 5.60002H11.644L8.556 8.99642C8.503 9.05473 8.462 9.12291 8.43535 9.19706C8.40869 9.27121 8.3969 9.34988 8.40066 9.42859C8.40441 9.5073 8.42362 9.58449 8.45721 9.65578C8.4908 9.72706 8.53809 9.79102 8.5964 9.84402C8.65471 9.89703 8.72288 9.93802 8.79703 9.96468C8.87119 9.99133 8.94986 10.0031 9.02857 9.99937C9.10727 9.99562 9.18447 9.9764 9.25575 9.94281C9.32703 9.90923 9.391 9.86193 9.444 9.80362L13.444 5.40362Z" fill="black"/>
+</svg>
Index: src/icons/mcp/Case.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/Case.tsx b/src/icons/mcp/Case.tsx
new file mode 100644
--- /dev/null	(date 1755004212465)
+++ b/src/icons/mcp/Case.tsx	(date 1755004212465)
@@ -0,0 +1,17 @@
+import type { SVGProps } from "react";
+const SvgCase = (props: SVGProps<SVGSVGElement>) => (
+    <svg
+        xmlns="http://www.w3.org/2000/svg"
+        width="1em"
+        height="1em"
+        fill="none"
+        viewBox="0 0 16 16"
+        {...props}
+    >
+        <path
+            fill="#000"
+            d="M.533 12.8h14.934a.533.533 0 0 0 0-1.067H.533a.533.533 0 1 0 0 1.066M3.2 11.732v-6.94c0-.285.243-.527.53-.527h8.54c.293 0 .53.237.53.529v6.938h1.067V4.794c0-.88-.714-1.595-1.597-1.595H3.73a1.6 1.6 0 0 0-1.596 1.595v6.939zm3.24-3.051a.428.428 0 0 0 .685-.11l.74-1.483.558 2.233a.426.426 0 0 0 .78.116l.975-1.627.452.452a.427.427 0 1 0 .602-.603l-.836-.838a.427.427 0 0 0-.668.082l-.726 1.21-.59-2.351a.427.427 0 0 0-.794-.088L6.626 7.66l-.42-.418a.43.43 0 0 0-.492-.08l-.839.418a.427.427 0 1 0 .382.763l.562-.281z"
+        />
+    </svg>
+);
+export default SvgCase;
Index: src/icons/mcp/ArrowRight1.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/ArrowRight1.tsx b/src/icons/mcp/ArrowRight1.tsx
new file mode 100644
--- /dev/null	(date 1755004212546)
+++ b/src/icons/mcp/ArrowRight1.tsx	(date 1755004212546)
@@ -0,0 +1,17 @@
+import type { SVGProps } from "react";
+const SvgArrowRight1 = (props: SVGProps<SVGSVGElement>) => (
+    <svg
+        xmlns="http://www.w3.org/2000/svg"
+        width="1em"
+        height="1em"
+        fill="none"
+        viewBox="0 0 14 10"
+        {...props}
+    >
+        <path
+            fill="#000"
+            d="M13.444 5.404a.6.6 0 0 0 0-.808l-4-4.4a.6.6 0 0 0-.888.808L11.644 4.4H.6a.6.6 0 0 0 0 1.2h11.044L8.556 8.996a.6.6 0 0 0 .888.808z"
+        />
+    </svg>
+);
+export default SvgArrowRight1;
