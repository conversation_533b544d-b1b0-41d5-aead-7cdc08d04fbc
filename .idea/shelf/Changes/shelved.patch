Index: src/comatestack/MCP/MCPSquare/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import styled from '@emotion/styled';\nimport {Flex, Form} from 'antd';\nimport {useCallback, useState} from 'react';\nimport {useDebouncedCallback} from 'huse';\nimport SquireFilter, {FilterValues, TabValues} from './SquireFilter';\nimport SquirePanel from './SquirePanel';\n\nconst Container = styled(Flex)`\n    width: 100%;\n    height: calc(100vh - 48px);\n    padding: 0 20px 16px;\n    overflow: auto;\n`;\n\nconst initialTabFormValue = {tab: 'all'};\nconst initialFilterFormValue: FilterValues = {\n    serverSourceType: 'all',\n    serverProtocolType: 'all',\n    labels: [-2],\n    viewOrder: 'DESC',\n};\nconst initialSearchParams = {\n    ...initialTabFormValue,\n    ...initialFilterFormValue,\n};\n\nconst MCPSquare = () => {\n    const [searchParams, setSearchParams] = useState<FilterValues & TabValues>(initialSearchParams);\n    const onFormChange = useCallback(\n        (formName: string, info: {changedFields: any}) => {\n            // 这里响应的是用户的修改，只取第一个就可以了。用户也没法一次改多个\n            const changedParams: Partial<FilterValues & TabValues> = {\n                [info.changedFields[0].name]: info.changedFields[0].value,\n            };\n            // 这俩互斥\n            if (info.changedFields[0].name[0] === 'viewOrder') {\n                changedParams.publishOrder = undefined;\n            } else if (info.changedFields[0].name[0] === 'publishOrder') {\n                changedParams.viewOrder = undefined;\n            }\n            setSearchParams(params => {\n                return {\n                    ...params,\n                    ...changedParams,\n                };\n            });\n        },\n        [setSearchParams]\n    );\n    const onFormChangeWithDebounce = useDebouncedCallback(onFormChange, 200);\n    return (\n        <Container vertical gap={16}>\n            <Form.Provider onFormChange={onFormChangeWithDebounce}>\n                <SquireFilter\n                    initialTabFormValue={initialTabFormValue}\n                    initialFilterFormValue={initialFilterFormValue}\n                />\n            </Form.Provider>\n            <SquirePanel searchParams={searchParams} />\n        </Container>\n    );\n};\n\nexport default MCPSquare;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/index.tsx b/src/comatestack/MCP/MCPSquare/index.tsx
--- a/src/comatestack/MCP/MCPSquare/index.tsx	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/comatestack/MCP/MCPSquare/index.tsx	(date 1755004212474)
@@ -4,6 +4,7 @@
 import {useDebouncedCallback} from 'huse';
 import SquireFilter, {FilterValues, TabValues} from './SquireFilter';
 import SquirePanel from './SquirePanel';
+import RegionNavigation from './RegionNavigation';
 
 const Container = styled(Flex)`
     width: 100%;
@@ -50,6 +51,7 @@
     const onFormChangeWithDebounce = useDebouncedCallback(onFormChange, 200);
     return (
         <Container vertical gap={16}>
+            <RegionNavigation />
             <Form.Provider onFormChange={onFormChangeWithDebounce}>
                 <SquireFilter
                     initialTabFormValue={initialTabFormValue}
Index: src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx b/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx
new file mode 100644
--- /dev/null	(date 1755004212299)
+++ b/src/comatestack/MCP/MCPSquare/RegionNavigation/index.tsx	(date 1755004212299)
@@ -0,0 +1,196 @@
+/* eslint-disable max-lines */
+import styled from '@emotion/styled';
+import {Flex, Typography} from 'antd';
+import {ReactNode} from 'react';
+import {IconArrowRight, IconCode, IconOps, IconTest} from '@/icons/mcp';
+
+const NavigationContainer = styled(Flex)`
+    margin-top: 21px;
+    gap: 20px;
+`;
+
+const RegionCard = styled.div<{ isActive?: boolean, disabled?: boolean, regionType?: string }>`
+    flex: 1;
+    padding: 24px 20px;
+    border: 1px solid #D9D9D9;
+    border-radius: 10px;
+    background: ${props => {
+        switch (props.regionType) {
+            case 'dev':
+                return 'linear-gradient(329.12deg, #E7F1FF -8.74%, #FFFFFF 92.24%)';
+            case 'ops':
+                return 'linear-gradient(328.39deg, #DEF3FF -8.86%, #FFFFFF 89.14%)';
+            case 'test':
+                return 'linear-gradient(328.02deg, #F0ECFE -8.92%, #FFFFFF 92.24%)';
+            default:
+                return 'linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%)';
+        }
+    }};
+    transition: all 0.3s ease;
+    position: relative;
+    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
+
+    &:hover {
+        ${props =>
+        !props.disabled
+            && `
+            border-color: #0083FF;
+            transform: translateY(-2px);
+            box-shadow: 0px 5px 16px 0px #00000021;
+        `}
+    }
+`;
+
+const CardHeader = styled(Flex)`
+    align-items: center;
+    justify-content: space-between;
+    margin-bottom: 12px;
+`;
+
+const IconWrapper = styled.div`
+    display: flex;
+    align-items: center;
+    justify-content: center;
+    margin-right: 12px;
+    svg {
+        font-size: 20px;
+    }
+`;
+
+const CardTitle = styled(Typography.Title)`
+    margin: 0 !important;
+    font-size: 16px !important;
+    font-weight: 600 !important;
+    line-height: 28px !important;
+`;
+
+const CardDescription = styled(Typography.Text)`
+    font-size: 12px;
+    line-height: 20px;
+    color: #5C5C5C;
+`;
+
+const HoverActionContainer = styled(Flex)`
+    align-items: center;
+    gap: 8px;
+`;
+
+const HoverText = styled.span`
+    color: #0083FF;
+    font-size: 12px;
+    line-height: 20px;
+    opacity: 0;
+    transition: opacity 0.3s ease;
+    white-space: nowrap;
+`;
+
+const ArrowIcon = styled(IconArrowRight)`
+    color: #8c8c8c;
+    transition: all 0.3s ease;
+    font-size: 16px;
+
+    svg {
+        transition: all 0.3s ease;
+    }
+
+    path {
+        fill: #8c8c8c !important;
+        transition: fill 0.3s ease;
+    }
+`;
+
+const StyledRegionCard = styled(RegionCard)`
+    &:hover {
+        ${HoverText} {
+            opacity: 1;
+        }
+
+        ${ArrowIcon} {
+            color: #0083FF;
+            path {
+                fill: #0083FF !important;
+            }
+        }
+    }
+`;
+
+interface RegionItem {
+    key: string;
+    title: string;
+    description: string;
+    icon: ReactNode;
+    status?: 'active' | 'coming';
+    disabled?: boolean;
+    onClick?: () => void;
+}
+
+interface Props {
+    activeRegion?: string;
+    onRegionChange?: (region: string) => void;
+}
+
+const RegionNavigation = ({activeRegion = 'dev', onRegionChange}: Props) => {
+    const regions: RegionItem[] = [
+        {
+            key: 'dev',
+            title: '开发专区',
+            description:
+                '面向运维的MCP中心，汇聚各类高质量工具，在通用场景、云原生、业务运维等各领域助力提升效率，分析根因，提升排障效率。',
+            icon: <IconCode />,
+            status: 'active',
+            disabled: false,
+        },
+        {
+            key: 'ops',
+            title: '运维专区',
+            description:
+                '面向运维的MCP中心，汇聚各类高质量工具，在通用场景、云原生、业务运维等各领域助力提升效率，分析根因，提升排障效率。',
+            icon: <IconOps />,
+            status: 'coming',
+            disabled: false,
+        },
+        {
+            key: 'test',
+            title: '测试专区（敬请期待）',
+            description:
+                '面向运维的MCP中心，汇聚各类高质量工具，在通用场景、云原生、业务运维等各领域助力提升效率，分析根因，提升排障效率。',
+            icon: <IconTest />,
+            status: 'coming',
+            disabled: false,
+        },
+    ];
+
+    const handleRegionClick = (region: RegionItem) => {
+        if (!region.disabled && region.status === 'active') {
+            onRegionChange?.(region.key);
+        }
+    };
+
+    return (
+        <NavigationContainer>
+            {regions.map(region => (
+                <StyledRegionCard
+                    key={region.key}
+                    isActive={activeRegion === region.key}
+                    disabled={region.disabled}
+                    regionType={region.key}
+                    onClick={() => handleRegionClick(region)}
+                >
+                    <CardHeader>
+                        <Flex>
+                            <IconWrapper>{region.icon}</IconWrapper>
+                            <CardTitle level={4}>{region.title}</CardTitle>
+                        </Flex>
+                        <HoverActionContainer>
+                            <HoverText>进入专区</HoverText>
+                            <ArrowIcon />
+                        </HoverActionContainer>
+                    </CardHeader>
+                    <CardDescription>{region.description}</CardDescription>
+                </StyledRegionCard>
+            ))}
+        </NavigationContainer>
+    );
+};
+
+export default RegionNavigation;
Index: src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, Space, Divider, Typography} from 'antd';\nimport styled from '@emotion/styled';\nimport {useCallback} from 'react';\nimport {useNavigate} from 'react-router-dom';\nimport {css} from '@emotion/css';\nimport {MCPDetailLink} from '@/links/mcp';\nimport {MCPCollectButton} from '@/components/MCP/MCPCollectButton';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport {apiPostViewCount} from '@/api/mcp';\nimport {IconEye} from '@/icons/mcp';\nimport MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';\nimport TagGroup from '@/components/MCP/TagGroup';\nimport MCPCard from '@/design/MCP/MCPCard';\nimport MCPServerAvatar from '@/components/MCP/MCPServerAvatar';\nimport {overflowHiddenCss} from '@/styles/components';\nimport PublishInfo from '@/components/MCP/PublishInfo';\nimport ServerTypeTag from './ServerTypeTag';\n\nconst containerCss = css`\n    padding: 16px 20px 12px;\n`;\n\nconst Gray = styled.span`\n    font-size: 12px;\n    line-height: 20px;\n`;\n\nconst TooltipContainer = styled.div`\n    max-height: 200px;\n    word-wrap: break-word;\n    overflow: auto;\n`;\n\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n}\nconst SquireMCPCard = ({server, refresh}: Props) => {\n    const {\n        id,\n        workspaceId,\n        name,\n        departmentName,\n        serverSourceType,\n        serverProtocolType,\n        description,\n        labels,\n        icon,\n        viewCount,\n        favorite,\n        publishTime,\n        publishUser,\n    } = server;\n    const navigate = useNavigate();\n\n    const handleClick = useCallback(\n        async () => {\n            await apiPostViewCount({mcpServerId: id});\n            navigate(MCPDetailLink.toUrl({mcpId: id}));\n        },\n        [navigate, id]\n    );\n\n    return (\n        <MCPCard vertical onClick={handleClick} className={containerCss}>\n            <ServerTypeTag style={{position: 'absolute', right: 0, top: 0}} type={serverSourceType} />\n            <Flex gap={14} align=\"center\">\n                <MCPServerAvatar icon={icon} />\n                <Flex vertical justify=\"space-between\" style={{overflow: 'hidden'}} gap={4}>\n                    <Typography.Title level={4} ellipsis>{name}</Typography.Title>\n                    <Typography.Text\n                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}\n                    >\n                        {departmentName || '暂无部门信息'}\n                    </Typography.Text>\n                </Flex>\n            </Flex>\n            <Typography.Paragraph\n                type=\"secondary\"\n                ellipsis={{rows: 2, tooltip: <TooltipContainer>{description}</TooltipContainer>}}\n                style={{margin: '16px 0 12px', height: 44}}\n            >\n                {description || '暂无描述'}\n            </Typography.Paragraph>\n            <Flex align=\"center\" style={{overflow: 'hidden'}} gap={4}>\n                <MCPServerProtocolTypeTag type={serverProtocolType} />\n                <TagGroup\n                    labels={labels.map(label => ({id: label.id, label: label.labelValue}))}\n                    prefix={null}\n                    style={{flexShrink: 1, overflow: 'hidden'}}\n                    color=\"light-purple\"\n                    gap={4}\n                />\n            </Flex>\n            <Divider style={{margin: '16px 0 8px'}} />\n            <Flex justify=\"space-between\" align=\"center\" className={overflowHiddenCss}>\n                <Flex align=\"center\" gap={6} className={overflowHiddenCss}>\n                    <Flex align=\"center\" gap={4}>\n                        <IconEye />{viewCount}\n                    </Flex>\n                    <PublishInfo username={publishUser} time={publishTime} />\n                </Flex>\n                <Gray />\n                <Space size={4}>\n                    <MCPCollectButton refresh={refresh} favorite={favorite} serverId={id} size=\"small\" />\n                    <MCPSubscribeButton\n                        refresh={refresh}\n                        showText\n                        workspaceId={workspaceId}\n                        id={id}\n                        size=\"small\"\n                    />\n                </Space>\n            </Flex>\n        </MCPCard>\n    );\n};\n\nexport default SquireMCPCard;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx b/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx
--- a/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.tsx	(date 1755057697902)
@@ -1,36 +1,34 @@
-import {Flex, Space, Divider, Typography} from 'antd';
-import styled from '@emotion/styled';
-import {useCallback} from 'react';
+import {Flex, Typography, Tooltip, Divider} from 'antd';
+import {useCallback, MouseEvent} from 'react';
 import {useNavigate} from 'react-router-dom';
-import {css} from '@emotion/css';
-import {MCPDetailLink} from '@/links/mcp';
+import {Button} from '@panda-design/components';
+import {cx} from '@emotion/css';
+import {MCPDetailLink, MCPPlaygroundLink} from '@/links/mcp';
 import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
 import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
 import {MCPServerBase} from '@/types/mcp/mcp';
 import {apiPostViewCount} from '@/api/mcp';
-import {IconEye} from '@/icons/mcp';
-import MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';
+import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
+import SvgEye from '@/icons/mcp/Eye';
 import TagGroup from '@/components/MCP/TagGroup';
 import MCPCard from '@/design/MCP/MCPCard';
 import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
-import {overflowHiddenCss} from '@/styles/components';
 import PublishInfo from '@/components/MCP/PublishInfo';
-import ServerTypeTag from './ServerTypeTag';
-
-const containerCss = css`
-    padding: 16px 20px 12px;
-`;
-
-const Gray = styled.span`
-    font-size: 12px;
-    line-height: 20px;
-`;
-
-const TooltipContainer = styled.div`
-    max-height: 200px;
-    word-wrap: break-word;
-    overflow: auto;
-`;
+import {
+    containerCss,
+    hoverActionsStyle,
+    DescriptionContainer,
+    DescriptionText,
+    EllipsisOverlay,
+    cardContentStyle,
+    protocolTextStyle,
+    departmentTextStyle,
+    dividerStyle,
+    statsContainerStyle,
+    iconStyle,
+    formatCount,
+    actionButtonHoverStyle,
+} from './SquireMCPCard.styles';
 
 interface Props {
     server: MCPServerBase;
@@ -61,57 +59,80 @@
         },
         [navigate, id]
     );
+    const handleViewCountClick = useCallback(
+        (e: MouseEvent) => {
+            e.stopPropagation();
+            navigate(MCPDetailLink.toUrl({mcpId: id, tab: 'overview'}));
+        },
+        [navigate, id]
+    );
+    const handlePlaygroundClick = useCallback(
+        (e: MouseEvent) => {
+            e.stopPropagation();
+            window.open(MCPPlaygroundLink.toUrl({serverId: id}), '_blank');
+        },
+        [id]
+    );
 
     return (
         <MCPCard vertical onClick={handleClick} className={containerCss}>
-            <ServerTypeTag style={{position: 'absolute', right: 0, top: 0}} type={serverSourceType} />
             <Flex gap={14} align="center">
                 <MCPServerAvatar icon={icon} />
-                <Flex vertical justify="space-between" style={{overflow: 'hidden'}} gap={4}>
+                <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
                     <Typography.Title level={4} ellipsis>{name}</Typography.Title>
-                    <Typography.Text
-                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}
-                    >
-                        {departmentName || '暂无部门信息'}
-                    </Typography.Text>
+                    <Flex align="center" gap={12}>
+                        <Typography.Text style={protocolTextStyle}>
+                            {getServerTypeText(serverSourceType)}
+                        </Typography.Text>
+                        <Typography.Text style={protocolTextStyle}>|</Typography.Text>
+                        <Typography.Text style={protocolTextStyle}>{serverProtocolType}</Typography.Text>
+                    </Flex>
                 </Flex>
             </Flex>
-            <Typography.Paragraph
-                type="secondary"
-                ellipsis={{rows: 2, tooltip: <TooltipContainer>{description}</TooltipContainer>}}
-                style={{margin: '16px 0 12px', height: 44}}
-            >
-                {description || '暂无描述'}
-            </Typography.Paragraph>
-            <Flex align="center" style={{overflow: 'hidden'}} gap={4}>
-                <MCPServerProtocolTypeTag type={serverProtocolType} />
-                <TagGroup
-                    labels={labels.map(label => ({id: label.id, label: label.labelValue}))}
-                    prefix={null}
-                    style={{flexShrink: 1, overflow: 'hidden'}}
-                    color="light-purple"
-                    gap={4}
-                />
-            </Flex>
-            <Divider style={{margin: '16px 0 8px'}} />
-            <Flex justify="space-between" align="center" className={overflowHiddenCss}>
-                <Flex align="center" gap={6} className={overflowHiddenCss}>
-                    <Flex align="center" gap={4}>
-                        <IconEye />{viewCount}
-                    </Flex>
-                    <PublishInfo username={publishUser} time={publishTime} />
-                </Flex>
-                <Gray />
-                <Space size={4}>
-                    <MCPCollectButton refresh={refresh} favorite={favorite} serverId={id} size="small" />
-                    <MCPSubscribeButton
-                        refresh={refresh}
-                        showText
-                        workspaceId={workspaceId}
-                        id={id}
-                        size="small"
-                    />
-                </Space>
+            <Tooltip title={description || '暂无描述'} placement="top">
+                <DescriptionContainer>
+                    <DescriptionText>{description || '暂无描述'}</DescriptionText>
+                    <EllipsisOverlay />
+                </DescriptionContainer>
+            </Tooltip>
+            <Typography.Text style={departmentTextStyle}>{departmentName || '暂无部门信息'}</Typography.Text>
+            <TagGroup
+                labels={labels.map(label => ({id: label.id, label: label.labelValue}))}
+                prefix={null}
+                style={{flexShrink: 1, overflow: 'hidden'}}
+                color="light-purple"
+                gap={4}
+            />
+            <Divider style={dividerStyle} />
+            <Flex justify="space-between" align="center">
+                <Flex align="center" gap={12}>
+                    <Tooltip title="浏览量">
+                        <Flex align="center" gap={4} onClick={handleViewCountClick} className={statsContainerStyle}>
+                            <SvgEye style={iconStyle} />
+                            {formatCount(viewCount)}
+                        </Flex>
+                    </Tooltip>
+                </Flex>
+                <PublishInfo username={publishUser} time={publishTime} />
+            </Flex>
+            <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
+                <MCPCollectButton
+                    refresh={refresh}
+                    favorite={favorite}
+                    serverId={id}
+                    className={cx(actionButtonHoverStyle)}
+                    showText={false}
+                    iconColor="#0083FF"
+                />
+                <MCPSubscribeButton
+                    refresh={refresh}
+                    workspaceId={workspaceId}
+                    id={id}
+                    className={cx(actionButtonHoverStyle)}
+                    showText={false}
+                    iconColor="#0083FF"
+                />
+                <Button type="primary" onClick={handlePlaygroundClick}>去MCP Playground使用</Button>
             </Flex>
         </MCPCard>
     );
Index: src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, Divider, Typography} from 'antd';\nimport {memo, useCallback, useMemo} from 'react';\nimport {useNavigate} from 'react-router-dom';\nimport {css} from '@emotion/css';\nimport styled from '@emotion/styled';\nimport {MCPEditLink} from '@/links/mcp';\nimport {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport {useMCPWorkspaceId} from '@/components/MCP/hooks';\nimport {MCPDetailButton} from '@/components/MCP/MCPDetailButton';\nimport MCPServerAvatar from '@/components/MCP/MCPServerAvatar';\nimport MCPCard from '@/design/MCP/MCPCard';\nimport MCPServerTypeTag from '@/components/MCP/MCPServerTypeTag';\nimport MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';\nimport TagGroup from '@/components/MCP/TagGroup';\nimport UpdateInfo from '@/components/MCP/UpdateInfo';\n\nconst containerCss = css`\n    padding: 0 20px 12px;\n`;\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n}\n\nconst TooltipContainer = styled.div`\n    max-height: 200px;\n    word-wrap: break-word;\n    overflow: auto;\n`;\n\nconst SpaceMCPCard = ({server, refresh}: Props) => {\n    const spaceId = useMCPWorkspaceId();\n    const navigate = useNavigate();\n\n    const handleClick = useCallback(\n        () => {\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const tags = useMemo(\n        () => (server.labels ?? []).map(label => label.labelValue),\n        [server.labels]\n    );\n\n    return (\n        <MCPCard vertical onClick={handleClick} className={containerCss}>\n            <Flex align=\"flex-end\" gap={14} style={{overflow: 'hidden', marginRight: '-20px'}}>\n                <MCPServerAvatar icon={server.icon} style={{marginBottom: 4}} />\n                <Flex vertical style={{overflow: 'hidden', flex: 1}}>\n                    <Flex align=\"flex-start\" justify=\"space-between\" gap={8}>\n                        <Typography.Title level={4} style={{margin: '16px 0 4px'}} ellipsis>\n                            {server.name}\n                        </Typography.Title>\n                        <MCPReleaseStatus\n                            status={server.serverStatus}\n                            publishType={server.serverPublishType}\n                            style={{flexShrink: 0}}\n                        />\n                    </Flex>\n                    <Typography.Text\n                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}\n                    >\n                        {server.departmentName || '暂无部门信息'}\n                    </Typography.Text>\n                </Flex>\n            </Flex>\n            <Typography.Paragraph\n                type=\"secondary\"\n                ellipsis={{tooltip: <TooltipContainer>{server.description}</TooltipContainer>, rows: 2}}\n                style={{margin: '16px 0 12px', height: 44}}\n            >\n                {server.description || '暂无描述'}\n            </Typography.Paragraph>\n            <Flex gap={4}>\n                <MCPServerTypeTag type={server.serverSourceType} />\n                <MCPServerProtocolTypeTag type={server.serverProtocolType} />\n                <TagGroup\n                    labels={tags.map((label, index) => ({id: index, label}))}\n                    color=\"light-purple\"\n                    prefix={null}\n                    style={{flexShrink: 1, overflow: 'hidden'}}\n                    gap={4}\n                />\n            </Flex>\n            <Divider style={{margin: '16px 0 8px', borderColor: 'rgba(75, 108, 159, 0.15)'}} />\n            <Flex justify=\"space-between\" align=\"center\" gap={4}>\n                <UpdateInfo username={server.lastModifyUser} time={server.lastModifyTime} variant=\"space-card\" />\n                <Flex align=\"center\" gap={8}>\n                    <MCPDetailButton serverId={server.id} size=\"small\" />\n                    <Divider\n                        type=\"vertical\"\n                        style={{height: 16, margin: 0}}\n                    />\n                    <MCPSubscribeButton\n                        refresh={refresh}\n                        id={server.id}\n                        // 这个 Server 是空间下的，后端没有返回 Server 的 workspaceId\n                        // 所以这里用的是 页面级别的 workspaceId\n                        workspaceId={spaceId}\n                        showText\n                        showCount\n                        size=\"small\"\n                    />\n                </Flex>\n            </Flex>\n        </MCPCard>\n    );\n};\n\nexport default memo(SpaceMCPCard);\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
--- a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(date 1755082439442)
@@ -1,42 +1,52 @@
-import {Flex, Divider, Typography} from 'antd';
-import {memo, useCallback, useMemo} from 'react';
+/* eslint-disable max-lines */
+import {Flex, Divider, Typography, Tooltip} from 'antd';
+import {memo, useCallback, MouseEvent, useMemo} from 'react';
 import {useNavigate} from 'react-router-dom';
-import {css} from '@emotion/css';
-import styled from '@emotion/styled';
-import {MCPEditLink} from '@/links/mcp';
-import {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';
+import {Button} from '@panda-design/components';
+import cx from 'classnames';
+import {MCPDetailLink, MCPEditLink, MCPPlaygroundLink} from '@/links/mcp';
 import {MCPServerBase} from '@/types/mcp/mcp';
 import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
 import {useMCPWorkspaceId} from '@/components/MCP/hooks';
-import {MCPDetailButton} from '@/components/MCP/MCPDetailButton';
 import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
 import MCPCard from '@/design/MCP/MCPCard';
-import MCPServerTypeTag from '@/components/MCP/MCPServerTypeTag';
-import MCPServerProtocolTypeTag from '@/components/MCP/MCPServerProtocolTypeTag';
+import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
 import TagGroup from '@/components/MCP/TagGroup';
-import UpdateInfo from '@/components/MCP/UpdateInfo';
+import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
+import PublishInfo from '@/components/MCP/PublishInfo';
+import SvgEye from '@/icons/mcp/Eye';
+import {
+    actionButtonHoverStyle,
+    cardContentStyle, containerCss,
+    DescriptionContainer,
+    DescriptionText,
+    dividerStyle,
+    EllipsisOverlay,
+    formatCount,
+    hoverActionsStyle,
+    iconStyle,
+    protocolTextStyle,
+    statsContainerStyle,
+} from '../../MCPSquare/SquirePanel/SquireMCPCard.styles';
 
-const containerCss = css`
-    padding: 0 20px 12px;
-`;
 interface Props {
     server: MCPServerBase;
     refresh: () => void;
 }
 
-const TooltipContainer = styled.div`
-    max-height: 200px;
-    word-wrap: break-word;
-    overflow: auto;
-`;
-
 const SpaceMCPCard = ({server, refresh}: Props) => {
     const spaceId = useMCPWorkspaceId();
     const navigate = useNavigate();
 
     const handleClick = useCallback(
         () => {
-            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));
+            navigate(
+                MCPEditLink.toUrl({
+                    workspaceId: spaceId,
+                    mcpId: server.id,
+                    activeTab: 'tools',
+                })
+            );
         },
         [navigate, spaceId, server.id]
     );
@@ -46,66 +56,112 @@
         [server.labels]
     );
 
+    const handleViewCountClick = useCallback(
+        (e: MouseEvent) => {
+            e.stopPropagation();
+            navigate(
+                MCPDetailLink.toUrl({mcpId: server.id, tab: 'overview'})
+            );
+        },
+        [navigate, server.id]
+    );
+    const handlePlaygroundClick = useCallback(
+        (e: MouseEvent) => {
+            e.stopPropagation();
+            window.open(
+                MCPPlaygroundLink.toUrl({serverId: server.id}),
+                '_blank'
+            );
+        },
+        [server.id]
+    );
+
     return (
         <MCPCard vertical onClick={handleClick} className={containerCss}>
-            <Flex align="flex-end" gap={14} style={{overflow: 'hidden', marginRight: '-20px'}}>
-                <MCPServerAvatar icon={server.icon} style={{marginBottom: 4}} />
-                <Flex vertical style={{overflow: 'hidden', flex: 1}}>
-                    <Flex align="flex-start" justify="space-between" gap={8}>
-                        <Typography.Title level={4} style={{margin: '16px 0 4px'}} ellipsis>
-                            {server.name}
-                        </Typography.Title>
-                        <MCPReleaseStatus
-                            status={server.serverStatus}
-                            publishType={server.serverPublishType}
-                            style={{flexShrink: 0}}
-                        />
-                    </Flex>
-                    <Typography.Text
-                        style={{color: '#8f8f8f', fontSize: 12, lineHeight: '20px'}}
-                    >
-                        {server.departmentName || '暂无部门信息'}
-                    </Typography.Text>
+            <Flex gap={14} align="center">
+                <MCPServerAvatar icon={server.icon} />
+                <Flex
+                    vertical
+                    justify="space-between"
+                    style={cardContentStyle}
+                    gap={4}
+                >
+                    <Typography.Title level={4} ellipsis>
+                        {server.name}
+                    </Typography.Title>
+                    <Flex align="center" gap={12}>
+                        <Typography.Text style={protocolTextStyle}>
+                            {getServerTypeText(server.serverSourceType)}
+                        </Typography.Text>
+                        <Typography.Text style={protocolTextStyle}>
+                            |
+                        </Typography.Text>
+                        <Typography.Text style={protocolTextStyle}>
+                            {server.serverProtocolType}
+                        </Typography.Text>
+                    </Flex>
                 </Flex>
             </Flex>
-            <Typography.Paragraph
-                type="secondary"
-                ellipsis={{tooltip: <TooltipContainer>{server.description}</TooltipContainer>, rows: 2}}
-                style={{margin: '16px 0 12px', height: 44}}
-            >
-                {server.description || '暂无描述'}
-            </Typography.Paragraph>
-            <Flex gap={4}>
-                <MCPServerTypeTag type={server.serverSourceType} />
-                <MCPServerProtocolTypeTag type={server.serverProtocolType} />
-                <TagGroup
-                    labels={tags.map((label, index) => ({id: index, label}))}
-                    color="light-purple"
-                    prefix={null}
-                    style={{flexShrink: 1, overflow: 'hidden'}}
-                    gap={4}
+            <Tooltip title={server.description || '暂无描述'} placement="top">
+                <DescriptionContainer>
+                    <DescriptionText>
+                        {server.description || '暂无描述'}
+                    </DescriptionText>
+                    <EllipsisOverlay />
+                </DescriptionContainer>
+            </Tooltip>
+            <TagGroup
+                labels={tags.map((label, index) => ({id: index, label}))}
+                color="light-purple"
+                prefix={null}
+                style={{flexShrink: 1, overflow: 'hidden'}}
+                gap={4}
+            />
+            <Divider style={dividerStyle} />
+            <Flex justify="space-between" align="center">
+                <Flex align="center" gap={12}>
+                    <Tooltip title="浏览量">
+                        <Flex
+                            align="center"
+                            gap={4}
+                            onClick={handleViewCountClick}
+                            className={statsContainerStyle}
+                        >
+                            <SvgEye style={iconStyle} />
+                            {formatCount(server.viewCount)}
+                        </Flex>
+                    </Tooltip>
+                </Flex>
+                <PublishInfo
+                    username={server.publishUser}
+                    time={server.publishTime}
                 />
             </Flex>
-            <Divider style={{margin: '16px 0 8px', borderColor: 'rgba(75, 108, 159, 0.15)'}} />
-            <Flex justify="space-between" align="center" gap={4}>
-                <UpdateInfo username={server.lastModifyUser} time={server.lastModifyTime} variant="space-card" />
-                <Flex align="center" gap={8}>
-                    <MCPDetailButton serverId={server.id} size="small" />
-                    <Divider
-                        type="vertical"
-                        style={{height: 16, margin: 0}}
-                    />
-                    <MCPSubscribeButton
-                        refresh={refresh}
-                        id={server.id}
-                        // 这个 Server 是空间下的，后端没有返回 Server 的 workspaceId
-                        // 所以这里用的是 页面级别的 workspaceId
-                        workspaceId={spaceId}
-                        showText
-                        showCount
-                        size="small"
-                    />
-                </Flex>
+            <Flex
+                align="center"
+                justify="space-between"
+                gap={10}
+                className={`hover-actions ${hoverActionsStyle}`}
+            >
+                <MCPCollectButton
+                    refresh={refresh}
+                    favorite={server.favorite}
+                    serverId={server.id}
+                    className={cx(actionButtonHoverStyle)}
+                    showText={false}
+                    iconColor="#0083FF"
+                />
+                <MCPSubscribeButton
+                    refresh={refresh}
+                    workspaceId={spaceId}
+                    id={server.id}
+                    className={cx(actionButtonHoverStyle)}
+                    showText={false}
+                    iconColor="#0083FF"
+                />
+                <Button type="primary" onClick={handlePlaygroundClick}>
+                    去MCP Playground使用
+                </Button>
             </Flex>
         </MCPCard>
     );
Index: src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, List} from 'antd';\nimport {useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport styled from '@emotion/styled';\nimport {apiGetSquareServerList} from '@/api/mcp';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';\nimport MCPEmpty from '@/design/MCP/MCPEmpty';\nimport {FilterValues, TabValues} from '../SquireFilter';\nimport {useLoadMore} from '../../MCPSpace/MCPListPanel/hooks';\nimport {ALL_LABELS, OTHER_LABELS} from '../SquireFilter/LabelsFilterContent';\nimport SquireMCPCard from './SquireMCPCard';\n\nconst Container = styled.div`\n    flex: 1;\n    overflow-y: auto;\n    ::-webkit-scrollbar {\n        display: none;\n    }\n`;\n\nconst PAGE_SIZE = 12;\n\nconst CARD_HEIGHT = 235;\n\nconst processEmptyText = (filterData?: FilterValues & TabValues) => {\n    if (filterData?.keywords?.trim()\n        || !(filterData?.labels?.length === 1 && filterData?.labels[0] === ALL_LABELS)\n        || filterData?.serverSourceType\n        || filterData?.serverProtocolType\n    ) {\n        return '暂无结果';\n    }\n    if (filterData?.favorite) {\n        return '暂无收藏的MCP Server';\n    }\n    if (filterData?.isMine) {\n        return '暂无发布的MCP Server';\n    }\n    return '暂无MCP Server';\n};\n\nconst formatSearchParams = (searchParams?: FilterValues & TabValues) => {\n    return {\n        viewOrder: searchParams.viewOrder,\n        publishOrder: searchParams.publishOrder,\n        ...(searchParams.tab !== 'all' ? {[searchParams.tab]: true} : {}),\n        labels: searchParams.labels?.includes(ALL_LABELS)\n            ? undefined\n            : searchParams.labels?.includes(OTHER_LABELS)\n                ? '-1'\n                : searchParams.labels?.map(label => label).join(','),\n        keywords: searchParams?.keywords?.trim() || undefined,\n        serverSourceType: searchParams?.serverSourceType === 'all' ? undefined : searchParams.serverSourceType,\n        serverProtocolType: searchParams.serverProtocolType === 'all' ? undefined : searchParams.serverProtocolType,\n    };\n};\n\ninterface Props {\n    searchParams?: FilterValues & TabValues; // Define the type according to your needs\n}\nconst SquirePanel = ({searchParams}: Props) => {\n    const [pageSize, setPageSize] = useState(PAGE_SIZE);\n    const api = useCallback(\n        (params: {current: number, limit: number}) => {\n            return apiGetSquareServerList({\n                ...formatSearchParams(searchParams),\n                platformType: 'hub',\n                size: params.limit,\n                pn: params.current,\n            });\n        },\n        [searchParams]\n    );\n    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, pageSize);\n\n    useEffect(\n        () => {\n            refresh();\n        },\n        [refresh]\n    );\n\n    const emptyText = useMemo(\n        () => processEmptyText(searchParams),\n        [searchParams]\n    );\n\n    useLayoutEffect(\n        () => {\n            const container = document.getElementById('scrollableDiv');\n            const height = container?.clientHeight || 0;\n            let rows = Math.ceil(height / CARD_HEIGHT);\n            // 因为每行可以有3个或2个卡片，所以需要确保pagesizepageSize是2和3的公倍数，所以需要调整rows的值为偶数。\n            rows = rows % 2 === 0 ? rows : rows + 1;\n            const maxItems = rows * 3;\n            if (maxItems > PAGE_SIZE) {\n                setPageSize(maxItems);\n            }\n        },\n        []\n    );\n\n    return (\n        <Container id=\"scrollableDiv\">\n            <InfiniteScroll\n                style={{overflow: 'none'}}\n                dataLength={list.length || 0}\n                next={loadMore}\n                hasMore={total > list.length}\n                loader={<Flex justify=\"center\" align=\"center\"><div>加载中...</div></Flex>}\n                scrollableTarget=\"scrollableDiv\"\n            >\n                {(\n                    <List\n                        grid={{\n                            gutter: 12,\n                            column: 2,\n                            xs: 2,\n                            sm: 3,\n                            md: 3,\n                            lg: 3,\n                            xl: 3,\n                            xxl: 3,\n                        }}\n                        dataSource={list}\n                        rowKey=\"id\"\n                        renderItem={server => (\n                            <List.Item>\n                                <SquireMCPCard refresh={refresh} key={server.id} server={server} />\n                            </List.Item>\n                        )}\n                        locale={{\n                            emptyText: (\n                                <MCPEmpty\n                                    description={(\n                                        <Flex justify=\"center\">\n                                            {emptyText}\n                                        </Flex>\n                                    )}\n                                />\n                            ),\n                        }}\n                    />\n                )}\n            </InfiniteScroll>\n            <CreateMCPAppModal />\n        </Container>\n    );\n};\n\nexport default SquirePanel;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx b/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx
--- a/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx	(revision dad5e372dc15febb2a568d9ff1453a594b98e4b5)
+++ b/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx	(date 1755004212569)
@@ -114,7 +114,7 @@
                 {(
                     <List
                         grid={{
-                            gutter: 12,
+                            gutter: 20,
                             column: 2,
                             xs: 2,
                             sm: 3,
Index: src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.styles.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.styles.ts b/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.styles.ts
new file mode 100644
--- /dev/null	(date 1755057050589)
+++ b/src/comatestack/MCP/MCPSquare/SquirePanel/SquireMCPCard.styles.ts	(date 1755057050589)
@@ -0,0 +1,129 @@
+import styled from '@emotion/styled';
+import {css} from '@emotion/css';
+import {colors} from '@/constants/colors';
+
+export const containerCss = css`
+    padding: 16px 20px 12px;
+    position: relative;
+    transition: all 0.3s ease;
+    &:hover {
+        position: relative;
+        z-index: 1;
+        background: ${colors.white};
+        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
+        border-radius: 6px;
+        padding-bottom: 48px;
+        margin-bottom: -48px;
+        width: auto;
+        .hover-actions {
+            opacity: 1;
+            min-height: 32px;
+            padding: 0 20px 16px;
+        }
+    }
+`;
+
+export const hoverActionsStyle = css`
+    position: absolute;
+    bottom: 0;
+    left: 0;
+    right: 0;
+    max-height: 0;
+    opacity: 0;
+    transition: all 0.3s ease;
+`;
+
+export const DescriptionContainer = styled.div`
+    margin: 15px 0 13px;
+    padding: 10px 12px 9px;
+    background-color: ${colors['gray-3']};
+    font-size: 14px;
+    line-height: 1.4;
+    position: relative;
+    height: 57px;
+    overflow: hidden;
+`;
+
+export const DescriptionText = styled.div`
+    display: -webkit-box;
+    -webkit-line-clamp: 2;
+    -webkit-box-orient: vertical;
+    text-overflow: ellipsis;
+    word-break: break-word;
+    overflow: hidden;
+`;
+
+export const EllipsisOverlay = styled.div`
+    position: absolute;
+    bottom: 9px;
+    right: 12px;
+    padding-left: 10px;
+    background: linear-gradient(to right, transparent, ${colors['gray-3']} 50%);
+    pointer-events: none;
+`;
+
+export const cardContentStyle = {
+    overflow: 'hidden',
+    flex: 1,
+};
+
+export const protocolTextStyle = {
+    color: colors['gray-7'],
+    fontSize: 12,
+    lineHeight: '20px',
+};
+
+export const departmentTextStyle = {
+    color: colors['gray-7'],
+    fontSize: 12,
+    marginBottom: 12,
+};
+
+export const dividerStyle = {
+    margin: '16px 0 8px',
+};
+
+export const statsContainerStyle = css`
+    cursor: pointer;
+    color: ${colors['gray-7']};
+    font-size: 12px;
+    line-height: 18px;
+    transition: color 0.2s ease;
+
+    &:hover {
+        color: ${colors.primary};
+    }
+`;
+
+export const iconStyle = {
+    width: 14,
+    height: 14,
+};
+
+export const formatCount = (count: number): string => {
+    if (count >= 10000) {
+        return `${Math.floor(count / 10000)}w+`;
+    }
+    if (count >= 1000) {
+        return `${Math.floor(count / 1000)}k+`;
+    }
+    return count.toString();
+};
+
+export const actionButtonHoverStyle = css`
+    flex: 1;
+    background-color: #F2F2F2;
+    border-radius: 4px;
+    border: none;
+    padding: 0;
+    display: flex;
+    align-items: center;
+    justify-content: center;
+    transition: all 0.2s ease;
+
+    &:hover {
+        background-color: #E6E6E6 !important;
+        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
+        transform: translateY(-1px);
+    }
+`;
