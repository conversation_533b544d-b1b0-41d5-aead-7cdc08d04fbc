Index: src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {useCallback, useEffect, useMemo, useState} from 'react';\nimport {Flex, List, Space} from 'antd';\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport styled from '@emotion/styled';\nimport {apiGetMCPServerListByWorkspace} from '@/api/mcp';\nimport {useMCPWorkspaceId} from '@/components/MCP/hooks';\nimport {MCPServerBase, MCPServerStatus} from '@/types/mcp/mcp';\nimport {RadioButtonGroup} from '@/components/MCP/RadioButtonGroup';\nimport {Gap} from '@/design/iplayground/Gap';\nimport CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';\nimport MCPEmpty from '@/design/MCP/MCPEmpty';\nimport SpaceMCPCard from './SpaceMCPCard';\nimport {useLoadMore} from './hooks';\n\nconst PAGE_SIZE = 18;\n\nconst Container = styled.div`\n    flex: 1;\n    overflow-y: auto;\n    ::-webkit-scrollbar {\n        display: none;\n    }\n`;\n\nconst StyledLabel = styled.span`\n    display: inline-flex;\n    gap: 16px;\n    align-items: center;\n    color: #8F8F8F;\n    &: after{\n        content: '';\n        width: 1px;\n        height: 16px;\n        background: #D9D9D9;\n    }\n`;\n\nconst MCPListPanel = () => {\n    const workspaceId = useMCPWorkspaceId();\n    const [selectedStatus, setSelectedStatus] = useState<MCPServerStatus | 'all'>('all');\n\n    const api = useCallback(\n        (params: {current: number, limit: number}) => {\n            return apiGetMCPServerListByWorkspace({\n                workspaceId,\n                size: params.limit,\n                pn: params.current,\n                status: selectedStatus !== 'all' ? selectedStatus : undefined,\n            });\n        },\n        [workspaceId, selectedStatus]\n    );\n\n    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, PAGE_SIZE);\n\n    // TODO：接口上线后移除\n    const dataSource = useMemo(\n        () => {\n            if (selectedStatus === 'all') {\n                return list;\n            }\n            return list?.filter(item => item.serverStatus === selectedStatus);\n        },\n        [list, selectedStatus]\n    );\n    // const [newFirst, setNewFirst] = useState(true);\n    useEffect(\n        () => {\n            refresh();\n        },\n        [refresh]\n    );\n\n    return (\n        <Container id=\"scrollableDiv\">\n            <Space direction=\"vertical\">\n                <Space>\n                    <StyledLabel>MCP状态</StyledLabel>\n                    <RadioButtonGroup\n                        value={selectedStatus}\n                        onChange={setSelectedStatus}\n                        options={[\n                            {label: '全部', value: 'all'},\n                            {label: '已发布', value: 'release'},\n                            {label: '草稿', value: 'draft'},\n                        ]}\n                    />\n                </Space>\n                {/* <Space>\n                    <span>发布时间</span>\n                    <Button\n                        onClick={() => setNewFirst(value => !value)}\n                        type=\"text\"\n                    >\n                        {newFirst ? '新到旧' : '旧到新'} <StyledIcon newFirst={newFirst} />\n                    </Button>\n                </Space> */}\n            </Space>\n            <Gap />\n            <InfiniteScroll\n                // 挂一个key，避免切换type的时候出问题\n                key={selectedStatus}\n                style={{overflow: 'none'}}\n                dataLength={list.length || 0}\n                next={loadMore}\n                hasMore={total > list.length}\n                loader={<Flex justify=\"center\" align=\"center\"><div>加载中...</div></Flex>}\n                // endMessage={<Divider plain>到底了</Divider>}\n                scrollableTarget=\"scrollableDiv\"\n            >\n                {(\n                    <List\n                        dataSource={dataSource}\n                        grid={{\n                            gutter: 12,\n                            column: 2,\n                            xs: 2,\n                            sm: 3,\n                            md: 3,\n                            lg: 3,\n                            xl: 3,\n                            xxl: 3,\n                        }}\n                        rowKey=\"id\"\n                        renderItem={item => (\n                            <List.Item key={item.id}>\n                                <SpaceMCPCard refresh={refresh} server={item} />\n                            </List.Item>\n                        )}\n                        locale={{\n                            emptyText: (\n                                <MCPEmpty\n                                    description={(\n                                        <Flex align=\"center\" justify=\"center\">\n                                            <span>暂无MCP Server</span>\n                                        </Flex>\n                                    )}\n                                />\n                            ),\n                        }}\n                    />\n                )}\n            </InfiniteScroll>\n            <CreateMCPAppModal onSuccess={refresh} />\n        </Container>\n    );\n};\n\nexport default MCPListPanel;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx b/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx
--- a/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx	(revision ba445a64e238c8f297690ee2afdd8b33453276f2)
+++ b/src/comatestack/MCP/MCPSpace/MCPListPanel/index.tsx	(date 1755151625943)
@@ -112,14 +112,14 @@
                     <List
                         dataSource={dataSource}
                         grid={{
-                            gutter: 12,
+                            gutter: 20,
                             column: 2,
                             xs: 2,
                             sm: 3,
                             md: 3,
                             lg: 3,
                             xl: 3,
-                            xxl: 3,
+                            xxl: 4,
                         }}
                         rowKey="id"
                         renderItem={item => (
Index: .gitignore
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+># See https://help.github.com/articles/ignoring-files/ for more about ignoring files.\n\n# dependencies\n/node_modules\n/.pnp\n.pnp.js\n\n# testing\n/coverage\n\n# production\n/build\n/output\n/.comate-f2c\n\n# misc\n.DS_Store\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.gitignore b/.gitignore
--- a/.gitignore	(revision ba445a64e238c8f297690ee2afdd8b33453276f2)
+++ b/.gitignore	(date 1755096225783)
@@ -23,3 +23,30 @@
 npm-debug.log*
 yarn-debug.log*
 yarn-error.log*
+.idea/.gitignore
+.idea/comate-stack-fe.iml
+.idea/modules.xml
+.idea/vcs.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+/.augment/rules/mode.md
+/.idea/jsLinters/eslint.xml
+.idea/.gitignore
+.idea/comate-stack-fe.iml
+.idea/modules.xml
+.idea/vcs.xml
+.idea/codeStyles/codeStyleConfig.xml
+.idea/codeStyles/Project.xml
+.idea/inspectionProfiles/Project_Default.xml
+/.augment/rules/mode.md
+/.idea/jsLinters/eslint.xml
+.idea/workspace.xml
+.idea/shelf/Changes.xml
+.idea/shelf/Changes1.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10__Changes_.xml
+.idea/shelf/Changes/shelved.patch
+.idea/shelf/Changes1/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_12_21_10_\[Changes]/shelved.patch
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18__Changes_.xml
+.idea/shelf/Uncommitted_changes_before_Update_at_2025_8_13_22_18_\[Changes]/shelved.patch
Index: src/icons/mcp/index.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import type { FC, SVGProps } from \"react\";\nimport { createIcon } from \"@panda-design/components\";\nimport SendActived from \"./SendActived\";\nimport AiTools1 from \"./AiTools1\";\nimport AiTools2 from \"./AiTools2\";\nimport AiTools3 from \"./AiTools3\";\nimport AiTools4 from \"./AiTools4\";\nimport Alert from \"./Alert\";\nimport ArrowRight from \"./ArrowRight\";\nimport Case from \"./Case\";\nimport Code from \"./Code\";\nimport Comment from \"./Comment\";\nimport Copy from \"./Copy\";\nimport Debug from \"./Debug\";\nimport Delete from \"./Delete\";\nimport Detail from \"./Detail\";\nimport Elipsis from \"./Elipsis\";\nimport ExitFullscreen from \"./ExitFullscreen\";\nimport Eye from \"./Eye\";\nimport Fullscreen from \"./Fullscreen\";\nimport Import from \"./Import\";\nimport LeftOutlined from \"./LeftOutlined\";\nimport LightMySpcae from \"./LightMySpcae\";\nimport LightPlayground from \"./LightPlayground\";\nimport List from \"./List\";\nimport Local from \"./Local\";\nimport LocalMcp from \"./LocalMcp\";\nimport McpAvatar from \"./McpAvatar\";\nimport MySpcae from \"./MySpcae\";\nimport OffcialExample from \"./OffcialExample\";\nimport Ops from \"./Ops\";\nimport Organization from \"./Organization\";\nimport Params from \"./Params\";\nimport Playground from \"./Playground\";\nimport PlaygroundConfig from \"./PlaygroundConfig\";\nimport Refresh from \"./Refresh\";\nimport Remote from \"./Remote\";\nimport RemoteMcp from \"./RemoteMcp\";\nimport Result from \"./Result\";\nimport RightArrow from \"./RightArrow\";\nimport Send from \"./Send\";\nimport Setting from \"./Setting\";\nimport ShowMore from \"./ShowMore\";\nimport SortAsc from \"./SortAsc\";\nimport SortDesc from \"./SortDesc\";\nimport Sse from \"./Sse\";\nimport Standard from \"./Standard\";\nimport Stdio from \"./Stdio\";\nimport StdioMcp from \"./StdioMcp\";\nimport Step01 from \"./Step01\";\nimport Step02 from \"./Step02\";\nimport Step03 from \"./Step03\";\nimport Step04 from \"./Step04\";\nimport StopGenerate from \"./StopGenerate\";\nimport Subscribe from \"./Subscribe\";\nimport SubscribeFilled from \"./SubscribeFilled\";\nimport Subtract from \"./Subtract\";\nimport Tag from \"./Tag\";\nimport Test from \"./Test\";\nimport Tool from \"./Tool\";\nimport Unfold from \"./Unfold\";\nimport SubscribeBlue from \"./SubscribeBlue\";\n\nexport const IconSendActived = createIcon(SendActived);\nexport const IconAiTools1 = createIcon(AiTools1);\nexport const IconAiTools2 = createIcon(AiTools2);\nexport const IconAiTools3 = createIcon(AiTools3);\nexport const IconAiTools4 = createIcon(AiTools4);\nexport const IconAlert = createIcon(Alert);\nexport const IconArrowRight = createIcon(ArrowRight);\nexport const IconCase = createIcon(Case);\nexport const IconCode = createIcon(Code);\nexport const IconComment = createIcon(Comment);\nexport const IconCopy = createIcon(Copy);\nexport const IconDebug = createIcon(Debug);\nexport const IconDelete = createIcon(Delete);\nexport const IconDetail = createIcon(Detail);\nexport const IconElipsis = createIcon(Elipsis);\nexport const IconExitFullscreen = createIcon(ExitFullscreen);\nexport const IconEye = createIcon(Eye);\nexport const IconFullscreen = createIcon(Fullscreen);\nexport const IconImport = createIcon(Import);\nexport const IconLeftOutlined = createIcon(LeftOutlined);\nexport const IconLightMySpcae = createIcon(LightMySpcae);\nexport const IconLightPlayground = createIcon(LightPlayground);\nexport const IconList = createIcon(List);\nexport const IconLocal = createIcon(Local);\nexport const IconLocalMcp = createIcon(LocalMcp);\nexport const IconMcpAvatar = createIcon(McpAvatar);\nexport const IconMySpcae = createIcon(MySpcae);\nexport const IconOffcialExample = createIcon(OffcialExample);\nexport const IconOps = createIcon(Ops);\nexport const IconOrganization = createIcon(Organization);\nexport const IconParams = createIcon(Params);\nexport const IconPlayground = createIcon(Playground);\nexport const IconPlaygroundConfig = createIcon(PlaygroundConfig);\nexport const IconRefresh = createIcon(Refresh);\nexport const IconRemote = createIcon(Remote);\nexport const IconRemoteMcp = createIcon(RemoteMcp);\nexport const IconResult = createIcon(Result);\nexport const IconRightArrow = createIcon(RightArrow);\nexport const IconSend = createIcon(Send);\nexport const IconSetting = createIcon(Setting);\nexport const IconShowMore = createIcon(ShowMore);\nexport const IconSortAsc = createIcon(SortAsc);\nexport const IconSortDesc = createIcon(SortDesc);\nexport const IconSse = createIcon(Sse);\nexport const IconStandard = createIcon(Standard);\nexport const IconStdio = createIcon(Stdio);\nexport const IconStdioMcp = createIcon(StdioMcp);\nexport const IconStep01 = createIcon(Step01);\nexport const IconStep02 = createIcon(Step02);\nexport const IconStep03 = createIcon(Step03);\nexport const IconStep04 = createIcon(Step04);\nexport const IconStopGenerate = createIcon(StopGenerate);\nexport const IconSubscribe = createIcon(Subscribe);\nexport const IconSubscribeBlue = createIcon(SubscribeBlue);\nexport const IconSubscribeFilled = createIcon(SubscribeFilled);\nexport const IconSubtract = createIcon(Subtract);\nexport const IconTag = createIcon(Tag);\nexport const IconTest = createIcon(Test);\nexport const IconTool = createIcon(Tool);\nexport const IconUnfold = createIcon(Unfold);\n\nexport const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {\n    SendActived: IconSendActived,\n    aiTools1: IconAiTools1,\n    aiTools2: IconAiTools2,\n    aiTools3: IconAiTools3,\n    aiTools4: IconAiTools4,\n    alert: IconAlert,\n    arrowRight: IconArrowRight,\n    case: IconCase,\n    code: IconCode,\n    comment: IconComment,\n    copy: IconCopy,\n    debug: IconDebug,\n    delete: IconDelete,\n    detail: IconDetail,\n    elipsis: IconElipsis,\n    exitFullscreen: IconExitFullscreen,\n    eye: IconEye,\n    fullscreen: IconFullscreen,\n    import: IconImport,\n    leftOutlined: IconLeftOutlined,\n    lightMySpcae: IconLightMySpcae,\n    lightPlayground: IconLightPlayground,\n    list: IconList,\n    local: IconLocal,\n    localMCP: IconLocalMcp,\n    mcpAvatar: IconMcpAvatar,\n    mySpcae: IconMySpcae,\n    offcialExample: IconOffcialExample,\n    ops: IconOps,\n    organization: IconOrganization,\n    params: IconParams,\n    playground: IconPlayground,\n    playgroundConfig: IconPlaygroundConfig,\n    refresh: IconRefresh,\n    remote: IconRemote,\n    remoteMCP: IconRemoteMcp,\n    result: IconResult,\n    rightArrow: IconRightArrow,\n    send: IconSend,\n    setting: IconSetting,\n    showMore: IconShowMore,\n    sortAsc: IconSortAsc,\n    sortDesc: IconSortDesc,\n    sse: IconSse,\n    standard: IconStandard,\n    stdio: IconStdio,\n    stdioMCP: IconStdioMcp,\n    step01: IconStep01,\n    step02: IconStep02,\n    step03: IconStep03,\n    step04: IconStep04,\n    stopGenerate: IconStopGenerate,\n    subscribe: IconSubscribe,\n    subscribeBlue: IconSubscribeBlue,\n    subscribeFilled: IconSubscribeFilled,\n    subtract: IconSubtract,\n    tag: IconTag,\n    test: IconTest,\n    tool: IconTool,\n    unfold: IconUnfold,\n};\n\nexport default iconsMap;\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/icons/mcp/index.ts b/src/icons/mcp/index.ts
--- a/src/icons/mcp/index.ts	(revision ba445a64e238c8f297690ee2afdd8b33453276f2)
+++ b/src/icons/mcp/index.ts	(date 1755155554404)
@@ -33,6 +33,7 @@
 import Params from "./Params";
 import Playground from "./Playground";
 import PlaygroundConfig from "./PlaygroundConfig";
+import Premium from "./Premium";
 import Refresh from "./Refresh";
 import Remote from "./Remote";
 import RemoteMcp from "./RemoteMcp";
@@ -94,6 +95,7 @@
 export const IconParams = createIcon(Params);
 export const IconPlayground = createIcon(Playground);
 export const IconPlaygroundConfig = createIcon(PlaygroundConfig);
+export const IconPremium = createIcon(Premium);
 export const IconRefresh = createIcon(Refresh);
 export const IconRemote = createIcon(Remote);
 export const IconRemoteMcp = createIcon(RemoteMcp);
@@ -156,6 +158,7 @@
     params: IconParams,
     playground: IconPlayground,
     playgroundConfig: IconPlaygroundConfig,
+    premium: IconPremium,
     refresh: IconRefresh,
     remote: IconRemote,
     remoteMCP: IconRemoteMcp,
Index: src/components/MCP/BaseMCPCard/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, Divider, Typography, Tooltip} from 'antd';\nimport {memo, MouseEvent, useMemo, ReactNode} from 'react';\nimport {Button} from '@panda-design/components';\nimport cx from 'classnames';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport MCPServerAvatar from '@/components/MCP/MCPServerAvatar';\nimport MCPCard from '@/design/MCP/MCPCard';\nimport {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';\nimport TagGroup from '@/components/MCP/TagGroup';\nimport {MCPCollectButton} from '@/components/MCP/MCPCollectButton';\nimport PublishInfo from '@/components/MCP/PublishInfo';\nimport UpdateInfo from '@/components/MCP/UpdateInfo';\nimport SvgEye from '@/icons/mcp/Eye';\nimport {\n    actionButtonHoverStyle,\n    cardContentStyle,\n    containerCss,\n    DescriptionContainer,\n    DescriptionText,\n    departmentTextStyle,\n    dividerStyle,\n    EllipsisOverlay,\n    formatCount,\n    hoverActionsStyle,\n    iconStyle,\n    protocolTextStyle,\n    statsContainerStyle,\n} from './BaseMCPCard.styles';\n\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n    showDepartment?: boolean;\n    workspaceId?: number;\n    onCardClick: () => void;\n    onViewCountClick: (e: MouseEvent) => void;\n    onPlaygroundClick?: (e: MouseEvent) => void;\n    renderActions?: () => ReactNode;\n    infoType?: 'publish' | 'update';\n}\n\nconst BaseMCPCard = ({\n    server,\n    refresh,\n    showDepartment = false,\n    workspaceId,\n    onCardClick,\n    onViewCountClick,\n    onPlaygroundClick,\n    renderActions,\n    infoType = 'publish',\n}: Props) => {\n    const tags = useMemo(\n        () => (server.labels ?? []).map((label, index) => ({\n            id: label.id || index,\n            label: label.labelValue,\n        })),\n        [server.labels]\n    );\n\n    return (\n        <MCPCard vertical onClick={onCardClick} className={containerCss}>\n            <Flex gap={14} align=\"center\">\n                <MCPServerAvatar icon={server.icon} />\n                <Flex vertical justify=\"space-between\" style={cardContentStyle} gap={4}>\n                    <Typography.Title level={4} ellipsis>\n                        {server.name}\n                    </Typography.Title>\n                    <Flex align=\"center\" gap={12}>\n                        <Typography.Text style={protocolTextStyle}>\n                            {getServerTypeText(server.serverSourceType)} | {server.serverProtocolType}\n                        </Typography.Text>\n                    </Flex>\n                </Flex>\n            </Flex>\n            <Tooltip title={server.description || '暂无描述'} placement=\"top\">\n                <DescriptionContainer>\n                    <DescriptionText>{server.description || '暂无描述'}</DescriptionText>\n                    <EllipsisOverlay />\n                </DescriptionContainer>\n            </Tooltip>\n            {showDepartment && (\n                <Typography.Text style={departmentTextStyle}>{server.departmentName || '暂无部门信息'}</Typography.Text>\n            )}\n            <TagGroup\n                labels={tags}\n                color=\"light-purple\"\n                prefix={null}\n                style={{flexShrink: 1, overflow: 'hidden'}}\n                gap={4}\n            />\n            <Divider style={dividerStyle} />\n            <Flex justify=\"space-between\" align=\"center\">\n                <Flex align=\"center\" gap={12}>\n                    <Tooltip title=\"浏览量\">\n                        <Flex\n                            align=\"center\"\n                            gap={4}\n                            onClick={onViewCountClick}\n                            className={statsContainerStyle}\n                        >\n                            <SvgEye style={iconStyle} />\n                            {formatCount(server.viewCount)}\n                        </Flex>\n                    </Tooltip>\n                </Flex>\n                {infoType === 'update' ? (\n                    <UpdateInfo username={server.lastModifyUser} time={server.lastModifyTime} variant=\"space-card\" />\n                ) : (\n                    <PublishInfo username={server.publishUser} time={server.publishTime} />\n                )}\n            </Flex>\n            <Flex align=\"center\" justify=\"space-between\" gap={10} className={`hover-actions ${hoverActionsStyle}`}>\n                {renderActions ? renderActions() : (\n                    <>\n                        <MCPCollectButton\n                            refresh={refresh}\n                            favorite={server.favorite}\n                            serverId={server.id}\n                            className={cx(actionButtonHoverStyle)}\n                            showText={false}\n                            iconColor=\"#0083FF\"\n                        />\n                        <MCPSubscribeButton\n                            refresh={refresh}\n                            workspaceId={workspaceId || server.workspaceId}\n                            id={server.id}\n                            className={cx(actionButtonHoverStyle)}\n                            showText={false}\n                            iconColor=\"#0083FF\"\n                        />\n                        <Button type=\"primary\" onClick={onPlaygroundClick}>\n                            去MCP Playground使用\n                        </Button>\n                    </>\n                )}\n            </Flex>\n        </MCPCard>\n    );\n};\n\nexport default memo(BaseMCPCard);\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/components/MCP/BaseMCPCard/index.tsx b/src/components/MCP/BaseMCPCard/index.tsx
--- a/src/components/MCP/BaseMCPCard/index.tsx	(revision ba445a64e238c8f297690ee2afdd8b33453276f2)
+++ b/src/components/MCP/BaseMCPCard/index.tsx	(date 1755155825858)
@@ -1,3 +1,4 @@
+/* eslint-disable max-lines */
 import {Flex, Divider, Typography, Tooltip} from 'antd';
 import {memo, MouseEvent, useMemo, ReactNode} from 'react';
 import {Button} from '@panda-design/components';
@@ -9,9 +10,8 @@
 import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
 import TagGroup from '@/components/MCP/TagGroup';
 import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
-import PublishInfo from '@/components/MCP/PublishInfo';
-import UpdateInfo from '@/components/MCP/UpdateInfo';
 import SvgEye from '@/icons/mcp/Eye';
+import SvgPremium from '@/icons/mcp/Premium';
 import {
     actionButtonHoverStyle,
     cardContentStyle,
@@ -49,7 +49,6 @@
     onViewCountClick,
     onPlaygroundClick,
     renderActions,
-    infoType = 'publish',
 }: Props) => {
     const tags = useMemo(
         () => (server.labels ?? []).map((label, index) => ({
@@ -62,7 +61,25 @@
     return (
         <MCPCard vertical onClick={onCardClick} className={containerCss}>
             <Flex gap={14} align="center">
-                <MCPServerAvatar icon={server.icon} />
+                <div style={{position: 'relative', display: 'inline-block'}}>
+                    <MCPServerAvatar
+                        icon={server.icon}
+                        style={{
+                            border: '2px solid',
+                            borderImageSource:
+                                'linear-gradient(237.19deg, #0183FF -52.14%, rgba(173, 215, 255, 0.6) 111.4%)',
+                            borderImageSlice: 1,
+                        }}
+                    />
+                    <SvgPremium
+                        style={{
+                            position: 'absolute',
+                            bottom: -7,
+                            right: -4,
+                            fontSize: '23px',
+                        }}
+                    />
+                </div>
                 <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
                     <Typography.Title level={4} ellipsis>
                         {server.name}
@@ -105,11 +122,6 @@
                         </Flex>
                     </Tooltip>
                 </Flex>
-                {infoType === 'update' ? (
-                    <UpdateInfo username={server.lastModifyUser} time={server.lastModifyTime} variant="space-card" />
-                ) : (
-                    <PublishInfo username={server.publishUser} time={server.publishTime} />
-                )}
             </Flex>
             <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
                 {renderActions ? renderActions() : (
Index: src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {memo, MouseEvent, useCallback} from 'react';\nimport {useNavigate} from 'react-router-dom';\nimport {Button} from '@panda-design/components';\nimport cx from 'classnames';\nimport {css} from '@emotion/css';\nimport {MCPEditLink, MCPSpaceDetailLink} from '@/links/mcp';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport {useMCPWorkspaceId} from '@/components/MCP/hooks';\nimport {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';\nimport BaseMCPCard from '@/components/MCP/BaseMCPCard';\nimport {actionButtonHoverStyle} from '@/components/MCP/BaseMCPCard/BaseMCPCard.styles';\nimport {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';\n\nconst cardContainerStyle = css`\n    position: relative;\n    &:hover {\n        z-index: 2;\n    }\n`;\n\ninterface Props {\n    server: MCPServerBase;\n    refresh: () => void;\n}\n\nconst SpaceMCPCard = ({server, refresh}: Props) => {\n    const spaceId = useMCPWorkspaceId();\n    const navigate = useNavigate();\n\n    const handleClick = useCallback(\n        () => {\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const handleViewCountClick = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            navigate(MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview'}));\n        },\n        [navigate, server.id]\n    );\n\n    const handleBasicInfoClick = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'basicInfo'}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const handleToolsConfigClick = useCallback(\n        (e: MouseEvent) => {\n            e.stopPropagation();\n            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));\n        },\n        [navigate, spaceId, server.id]\n    );\n\n    const renderActions = useCallback(\n        () => (\n            <>\n\n                <MCPSubscribeButton\n                    refresh={refresh}\n                    workspaceId={spaceId || server.workspaceId}\n                    id={server.id}\n                    className={cx(actionButtonHoverStyle)}\n                    showText={false}\n                    iconColor=\"#0083FF\"\n                />\n                <Button type=\"text\" onClick={handleBasicInfoClick} className={cx(actionButtonHoverStyle)}>\n                    基本信息\n                </Button>\n                <Button type=\"text\" onClick={handleToolsConfigClick} className={cx(actionButtonHoverStyle)}>\n                    工具配置\n                </Button>\n            </>\n        ),\n        [handleBasicInfoClick, handleToolsConfigClick, refresh, spaceId, server.workspaceId, server.id]\n    );\n\n    return (\n        <div className={cardContainerStyle}>\n            <BaseMCPCard\n                server={server}\n                refresh={refresh}\n                showDepartment={false}\n                workspaceId={spaceId}\n                onCardClick={handleClick}\n                onViewCountClick={handleViewCountClick}\n                renderActions={renderActions}\n                infoType=\"update\"\n            />\n            <MCPReleaseStatus\n                status={server.serverStatus}\n                publishType={server.serverPublishType}\n                style={{\n                    position: 'absolute',\n                    top: 1,\n                    right: 1,\n                    zIndex: 1,\n                }}\n            />\n        </div>\n    );\n};\n\nexport default memo(SpaceMCPCard);\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx
--- a/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(revision ba445a64e238c8f297690ee2afdd8b33453276f2)
+++ b/src/comatestack/MCP/MCPSpace/MCPListPanel/SpaceMCPCard.tsx	(date 1755143856167)
@@ -91,7 +91,6 @@
                 onCardClick={handleClick}
                 onViewCountClick={handleViewCountClick}
                 renderActions={renderActions}
-                infoType="update"
             />
             <MCPReleaseStatus
                 status={server.serverStatus}
Index: src/types/mcp/mcp.ts
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Path} from '@panda-design/path-form';\nimport type {JSONSchema7} from 'json-schema';\nimport {ChatMessage} from '../staff/chat';\n\n/* eslint-disable max-lines */\nexport interface MCPSpaceMember {\n    label: string;\n    value: string;\n    role: string;\n    nameType: string;\n}\n\nexport interface MCPSpace {\n    id: number;\n    name?: string;\n    description?: string;\n    cloudAccountIds?: string[];\n    member?: MCPSpaceMember[];\n}\n\nexport interface MCPServerParam {\n    name: string;\n    description: string;\n    dataType: string;\n    required: boolean;\n    value: string;\n    defaultValue?: string;\n}\n\nexport interface MCPServerTool {\n    id: number;\n    name: string;\n    description: string;\n    status: string;\n}\n\nexport interface MCPToolItem {\n    id: number;\n    name: string;\n    toolKey: string;\n    serviceId: number;\n    toolStatus: string;\n    description: string;\n    toolParams: {\n        toolParams: BaseParam[];\n        serverParams: BaseParam[];\n    };\n    toolConf: {\n        responseTemplate: string;\n        apiDefinition: APIInfo;\n        openapiConf: ApiDefinition;\n    };\n}\n\nexport interface APIInfo {\n    endpoint: string;\n    params: any;\n    systemParams: BaseParam[];\n}\n\nexport interface ServerConf {\n    serverSourceType: string;\n    serverConfig: string;\n    overview: string;\n    // 是个对象\n    serverExtension: any;\n    serverParams: ServerParam[];\n}\n\nexport interface ServerParam {\n    name: string;\n    type: string;\n    description: string;\n    defaultValue: string;\n    isHide: boolean;\n    required: boolean;\n}\n\nexport interface Visibility {\n    type: string;\n    content: string;\n}\n\nexport interface BaseParam {\n    name: string;\n    example?: string;\n    exampleValue?: string;\n    description?: string;\n    relateApiParamPath?: string;\n    type?: string;\n    refParam?: string;\n    dataType?: string;\n    required: boolean;\n    value?: string; // 输入值时\n    children?: BaseParam[]; // body时\n    key?: string; // 记录 paramList 中的 refParams\n    fieldPath?: Path; // 记录路径\n    canRelate: boolean; // 是否可以关联\n}\n\nexport type MCPServerStatus = 'draft' | 'release';\nexport type MCPServerType = 'openapi' | 'script' | 'external';\nexport type MCPServerProtocolType = 'SSE' | 'STDIO' | 'Streamable_HTTP';\nexport type MCPServerPublishType = [] | ['workspace'] | ['hub'];\n\nexport interface MCPServerBase {\n    id: number;\n    workspaceId: number;\n    viewCount: number;\n    favorite: boolean;\n    name: string;\n    serverKey: string;\n    description: string;\n    serverSourceType: MCPServerType;\n    serverProtocolType: MCPServerProtocolType;\n    serverStatus: MCPServerStatus;\n    serverConf: ServerConf;\n    serverParams: null | MCPServerParam[];\n    serverPublishType: MCPServerPublishType;\n    labels?: SpaceLabel[];\n    icon: string | null;\n    lastModifyTime: string;\n    lastModifyUser: string;\n    publishTime: string;\n    publishUser: string;\n    /* 官方示例的server包括下边两个字段 */\n    officialExample?: boolean;\n    originalId?: number;\n    /* 后端缺失 */\n    departmentName: string;\n    official?: boolean;\n    contacts: string[];\n}\n\nexport interface SpaceLabel {\n    id: number;\n    labelValue: string;\n    labelType: string;\n    content?: string;\n}\n\nexport interface RequestBody {\n    type: string;\n    parameters: any[];\n    jsonSchema: JSONSchema;\n    example: string;\n}\n\nexport interface JSONSchema {\n    type: string;\n    items: JSONSchema | {type: string};\n    properties: Record<string, Description | JSONSchema>;\n    required: string[];\n    description?: string;\n    name?: string;\n    'x-iapi-orders': string[];\n    'x-iapi-ignore-properties': any[];\n}\n\nexport interface Description {\n    type: string;\n    description?: string;\n    examples?: string[];\n    format?: string;\n    enum?: string[];\n    value?: string;\n}\n\ninterface Response {\n    id?: string;\n    code?: number;\n    contentType?: string;\n    jsonSchema?: JSONSchema7;\n    name?: string;\n}\n\ninterface responseExamples {\n    data?: string;\n    name?: string;\n    responseId?: string;\n}\n\nexport interface ApiDefinition {\n    name: string;\n    type: string;\n    path: string;\n    method: string;\n    description: string;\n    operation_id: string;\n    tags: string[];\n    auth: Record<string, unknown>;\n    parameters: Record<string, BaseParam[]>;\n    requestBody: RequestBody;\n    responses: Response[];\n    responseExamples: responseExamples[];\n    fileName?: string;\n}\n\nexport interface ApplicationBase {\n    applicationId: number;\n    applicationName: string;\n    ifSub: boolean;\n}\n\nexport interface AppListForMCPServerBase {\n    workspaceId: number;\n    workspaceName: string;\n    role: string;\n    applications: ApplicationBase[];\n}\n\nexport type MCPChatStatus = 'loading' | 'success' | 'error';\nexport type MCPMessageRole = 'user' | 'assistant' | 'system';\nexport type MCPToolCallStatus = 'success' | 'pending' | 'fail' | 'running';\n\nexport interface MCPToolInfo {\n    type: 'mcpTool';\n    serverName: string;\n    toolName: string;\n}\n\nexport interface MCPToolOutput {\n    mimeType: string;\n    content: string;\n}\n\nexport interface MCPToolCall {\n    type: 'toolCall';\n    tool: MCPToolInfo;\n    input: string; // stringified JSON\n    status: MCPToolCallStatus;\n    output: MCPToolOutput[];\n}\n\nexport interface MCPChatConfig {\n    modelId: string;\n    systemPrompt: string;\n}\n\nexport interface MCPChatSession {\n    messages: ChatMessage[];\n}\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/types/mcp/mcp.ts b/src/types/mcp/mcp.ts
--- a/src/types/mcp/mcp.ts	(revision ba445a64e238c8f297690ee2afdd8b33453276f2)
+++ b/src/types/mcp/mcp.ts	(date 1755138670847)
@@ -107,6 +107,7 @@
     id: number;
     workspaceId: number;
     viewCount: number;
+    callCount: number;
     favorite: boolean;
     name: string;
     serverKey: string;
