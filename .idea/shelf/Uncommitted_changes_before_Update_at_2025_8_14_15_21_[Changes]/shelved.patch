Index: src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>import {Flex, List} from 'antd';\nimport {useCallback, useEffect, useLayoutEffect, useMemo, useState} from 'react';\nimport InfiniteScroll from 'react-infinite-scroll-component';\nimport styled from '@emotion/styled';\nimport {apiGetSquareServerList} from '@/api/mcp';\nimport {MCPServerBase} from '@/types/mcp/mcp';\nimport CreateMCPAppModal from '@/components/MCP/CreateMCPAppButton/CreateMCPAppModal';\nimport MCPEmpty from '@/design/MCP/MCPEmpty';\nimport {FilterValues, TabValues} from '../SquireFilter';\nimport {useLoadMore} from '../../MCPSpace/MCPListPanel/hooks';\nimport {ALL_LABELS, OTHER_LABELS} from '../SquireFilter/LabelsFilterContent';\nimport SquireMCPCard from './SquireMCPCard';\n\nconst Container = styled.div`\n    flex: 1;\n    overflow-y: auto;\n    ::-webkit-scrollbar {\n        display: none;\n    }\n`;\n\nconst PAGE_SIZE = 12;\n\nconst CARD_HEIGHT = 235;\n\nconst processEmptyText = (filterData?: FilterValues & TabValues) => {\n    if (filterData?.keywords?.trim()\n        || !(filterData?.labels?.length === 1 && filterData?.labels[0] === ALL_LABELS)\n        || filterData?.serverSourceType\n        || filterData?.serverProtocolType\n    ) {\n        return '暂无结果';\n    }\n    if (filterData?.favorite) {\n        return '暂无收藏的MCP Server';\n    }\n    if (filterData?.isMine) {\n        return '暂无发布的MCP Server';\n    }\n    return '暂无MCP Server';\n};\n\nconst formatSearchParams = (searchParams?: FilterValues & TabValues) => {\n    return {\n        viewOrder: searchParams.viewOrder,\n        publishOrder: searchParams.publishOrder,\n        ...(searchParams.tab !== 'all' ? {[searchParams.tab]: true} : {}),\n        labels: searchParams.labels?.includes(ALL_LABELS)\n            ? undefined\n            : searchParams.labels?.includes(OTHER_LABELS)\n                ? '-1'\n                : searchParams.labels?.map(label => label).join(','),\n        keywords: searchParams?.keywords?.trim() || undefined,\n        serverSourceType: searchParams?.serverSourceType === 'all' ? undefined : searchParams.serverSourceType,\n        serverProtocolType: searchParams.serverProtocolType === 'all' ? undefined : searchParams.serverProtocolType,\n    };\n};\n\ninterface Props {\n    searchParams?: FilterValues & TabValues; // Define the type according to your needs\n}\nconst SquirePanel = ({searchParams}: Props) => {\n    const [pageSize, setPageSize] = useState(PAGE_SIZE);\n    const api = useCallback(\n        (params: {current: number, limit: number}) => {\n            return apiGetSquareServerList({\n                ...formatSearchParams(searchParams),\n                platformType: 'hub',\n                size: params.limit,\n                pn: params.current,\n            });\n        },\n        [searchParams]\n    );\n    const {loadMore, total, list, refresh} = useLoadMore<MCPServerBase>(api, pageSize);\n\n    useEffect(\n        () => {\n            refresh();\n        },\n        [refresh]\n    );\n\n    const emptyText = useMemo(\n        () => processEmptyText(searchParams),\n        [searchParams]\n    );\n\n    useLayoutEffect(\n        () => {\n            const container = document.getElementById('scrollableDiv');\n            const height = container?.clientHeight || 0;\n            let rows = Math.ceil(height / CARD_HEIGHT);\n            // 因为每行可以有3个或2个卡片，所以需要确保pagesizepageSize是2和3的公倍数，所以需要调整rows的值为偶数。\n            rows = rows % 2 === 0 ? rows : rows + 1;\n            const maxItems = rows * 3;\n            if (maxItems > PAGE_SIZE) {\n                setPageSize(maxItems);\n            }\n        },\n        []\n    );\n\n    return (\n        <Container id=\"scrollableDiv\">\n            <InfiniteScroll\n                style={{overflow: 'none'}}\n                dataLength={list.length || 0}\n                next={loadMore}\n                hasMore={total > list.length}\n                loader={<Flex justify=\"center\" align=\"center\"><div>加载中...</div></Flex>}\n                scrollableTarget=\"scrollableDiv\"\n            >\n                {(\n                    <List\n                        grid={{\n                            gutter: 20,\n                            column: 2,\n                            xs: 2,\n                            sm: 3,\n                            md: 3,\n                            lg: 3,\n                            xl: 3,\n                            xxl: 3,\n                        }}\n                        dataSource={list}\n                        rowKey=\"id\"\n                        renderItem={server => (\n                            <List.Item>\n                                <SquireMCPCard refresh={refresh} key={server.id} server={server} />\n                            </List.Item>\n                        )}\n                        locale={{\n                            emptyText: (\n                                <MCPEmpty\n                                    description={(\n                                        <Flex justify=\"center\">\n                                            {emptyText}\n                                        </Flex>\n                                    )}\n                                />\n                            ),\n                        }}\n                    />\n                )}\n            </InfiniteScroll>\n            <CreateMCPAppModal />\n        </Container>\n    );\n};\n\nexport default SquirePanel;\n
===================================================================
diff --git a/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx b/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx
--- a/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx	(revision ba445a64e238c8f297690ee2afdd8b33453276f2)
+++ b/src/comatestack/MCP/MCPSquare/SquirePanel/index.tsx	(date 1755142503368)
@@ -121,7 +121,7 @@
                             md: 3,
                             lg: 3,
                             xl: 3,
-                            xxl: 3,
+                            xxl: 4,
                         }}
                         dataSource={list}
                         rowKey="id"
